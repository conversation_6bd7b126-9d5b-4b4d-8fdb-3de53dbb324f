import {
	format,
	formatDistanceToNow,
	isValid,
	parseISO,
	isToday,
	isYesterday,
	isThisWeek,
	isThisYear,
} from 'date-fns';

/**
 * Utility functions for formatting dates using date-fns
 *
 * Examples:
 * - formatDate('2024-01-15T14:30:00Z') → "Jan 15, 2024"
 * - formatRelativeTime('2024-01-15T14:30:00Z') → "2 hours ago"
 * - formatSmartDate('2024-01-15T14:30:00Z') → "Today at 2:30 PM"
 * - formatProjectDate('2024-01-15T14:30:00Z') → "2 hours ago" or "Jan 15"
 * - formatFullDate('2024-01-15T14:30:00Z') → "January 15, 2024 at 2:30 PM"
 */

/**
 * Formats a date string or Date object to a readable format
 * @param date - Date string (ISO) or Date object
 * @param formatString - Optional format string (default: 'MMM d, yyyy')
 * @returns Formatted date string or 'Invalid date' if parsing fails
 */
export const formatDate = (
	date: string | Date | null | undefined,
	formatString: string = 'MMM d, yyyy',
): string => {
	if (!date) return 'Not available';

	try {
		const dateObj = typeof date === 'string' ? parseISO(date) : date;

		if (!isValid(dateObj)) {
			return 'Invalid date';
		}

		return format(dateObj, formatString);
	} catch (error) {
		console.warn('Error formatting date:', error);
		return 'Invalid date';
	}
};

/**
 * Formats a date to show time relative to now (e.g., "2 hours ago")
 * @param date - Date string (ISO) or Date object
 * @returns Relative time string or 'Invalid date' if parsing fails
 */
export const formatRelativeTime = (
	date: string | Date | null | undefined,
): string => {
	if (!date) return 'Not available';

	try {
		const dateObj = typeof date === 'string' ? parseISO(date) : date;

		if (!isValid(dateObj)) {
			return 'Invalid date';
		}

		return formatDistanceToNow(dateObj, { addSuffix: true });
	} catch (error) {
		console.warn('Error formatting relative time:', error);
		return 'Invalid date';
	}
};

/**
 * Formats a date with context-aware formatting
 * - Today: "Today at 2:30 PM"
 * - Yesterday: "Yesterday at 2:30 PM"
 * - This week: "Monday at 2:30 PM"
 * - This year: "Jan 15 at 2:30 PM"
 * - Other years: "Jan 15, 2023"
 * @param date - Date string (ISO) or Date object
 * @returns Context-aware formatted date string
 */
export const formatSmartDate = (
	date: string | Date | null | undefined,
): string => {
	if (!date) return 'Not available';

	try {
		const dateObj = typeof date === 'string' ? parseISO(date) : date;

		if (!isValid(dateObj)) {
			return 'Invalid date';
		}

		if (isToday(dateObj)) {
			return `Today at ${format(dateObj, 'h:mm a')}`;
		}

		if (isYesterday(dateObj)) {
			return `Yesterday at ${format(dateObj, 'h:mm a')}`;
		}

		if (isThisWeek(dateObj)) {
			return format(dateObj, "EEEE 'at' h:mm a");
		}

		if (isThisYear(dateObj)) {
			return format(dateObj, "MMM d 'at' h:mm a");
		}

		return format(dateObj, 'MMM d, yyyy');
	} catch (error) {
		console.warn('Error formatting smart date:', error);
		return 'Invalid date';
	}
};

/**
 * Formats a date for display in project cards
 * Shows relative time for recent dates, absolute date for older ones
 * @param date - Date string (ISO) or Date object
 * @returns Formatted date string optimized for project cards
 */
export const formatProjectDate = (
	date: string | Date | null | undefined,
): string => {
	if (!date) return 'Not available';

	try {
		const dateObj = typeof date === 'string' ? parseISO(date) : date;

		if (!isValid(dateObj)) {
			return 'Invalid date';
		}

		const now = new Date();
		const diffInDays = Math.floor(
			(now.getTime() - dateObj.getTime()) / (1000 * 60 * 60 * 24),
		);

		// Show relative time for dates within the last 7 days
		if (diffInDays <= 7) {
			return formatDistanceToNow(dateObj, { addSuffix: true });
		}

		// Show absolute date for older dates
		if (isThisYear(dateObj)) {
			return format(dateObj, 'MMM d');
		}

		return format(dateObj, 'MMM d, yyyy');
	} catch (error) {
		console.warn('Error formatting project date:', error);
		return 'Invalid date';
	}
};

/**
 * Formats a date with full details including time
 * @param date - Date string (ISO) or Date object
 * @returns Full formatted date string with time
 */
export const formatFullDate = (
	date: string | Date | null | undefined,
): string => {
	if (!date) return 'Not available';

	try {
		const dateObj = typeof date === 'string' ? parseISO(date) : date;

		if (!isValid(dateObj)) {
			return 'Invalid date';
		}

		return format(dateObj, "MMMM d, yyyy 'at' h:mm a");
	} catch (error) {
		console.warn('Error formatting full date:', error);
		return 'Invalid date';
	}
};

/**
 * Formats a date for form inputs (YYYY-MM-DD format)
 * @param date - Date string (ISO) or Date object
 * @returns Date string in YYYY-MM-DD format
 */
export const formatInputDate = (
	date: string | Date | null | undefined,
): string => {
	if (!date) return '';

	try {
		const dateObj = typeof date === 'string' ? parseISO(date) : date;

		if (!isValid(dateObj)) {
			return '';
		}

		return format(dateObj, 'yyyy-MM-dd');
	} catch (error) {
		console.warn('Error formatting input date:', error);
		return '';
	}
};

/**
 * Checks if a date string is valid
 * @param date - Date string to validate
 * @returns Boolean indicating if the date is valid
 */
export const isValidDate = (
	date: string | Date | null | undefined,
): boolean => {
	if (!date) return false;

	try {
		const dateObj = typeof date === 'string' ? parseISO(date) : date;
		return isValid(dateObj);
	} catch {
		return false;
	}
};
