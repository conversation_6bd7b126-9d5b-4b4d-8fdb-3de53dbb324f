import { memo } from 'react';
import {
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	Typo<PERSON>,
	CardT<PERSON>le,
	CardDescription,
} from '@/components/atoms';
import ProjectCardSkeleton from '@/components/molecules/ProjectCardSkeleton';

export interface Project {
	id: string;
	name: string;
	description: string;
	createdAt?: string;
	updatedAt?: string;
}

interface ProjectsGridProps {
	projects: Project[];
	isLoading?: boolean;
}

const ProjectCard = memo<{ project: Project }>(({ project }) => (
	<Card
		variant='outlined'
		size='medium'
		interactive
		className='h-full'>
		<CardHeader>
			<CardTitle>{project.name}</CardTitle>
			<CardDescription>{project.description}</CardDescription>
		</CardHeader>
		<CardContent>
			<Typography
				variant='body'
				color='secondary'>
				Created: {project.createdAt}
			</Typography>
			<Typography
				variant='body'
				color='secondary'>
				Updated: {project.updatedAt}
			</Typography>
		</CardContent>
		<CardFooter>
			<Button
				variant='primary'
				size='sm'>
				View
			</Button>
		</CardFooter>
	</Card>
));

ProjectCard.displayName = 'ProjectCard';

const ProjectsGrid = memo<ProjectsGridProps>(
	({ projects, isLoading = false }) => {
		if (isLoading) {
			// Show skeleton cards while loading for better UX
			return (
				<div className='grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-300 mb-400'>
					{Array.from({ length: 6 }).map((_, index) => (
						<ProjectCardSkeleton key={index} />
					))}
				</div>
			);
		}

		if (projects.length === 0) {
			return (
				<div className='flex flex-col items-center justify-center py-12'>
					<Typography
						variant='h3'
						color='secondary'
						className='mb-2'>
						No projects yet
					</Typography>
					<Typography
						variant='body'
						color='tertiary'>
						Create your first project to get started
					</Typography>
				</div>
			);
		}

		return (
			<div className='grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-300 mb-400'>
				{projects.map((project) => (
					<ProjectCard
						key={project.id}
						project={project}
					/>
				))}
			</div>
		);
	},
);

ProjectsGrid.displayName = 'ProjectsGrid';

export default ProjectsGrid;
