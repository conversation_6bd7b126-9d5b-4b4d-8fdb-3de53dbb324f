import { useState, useCallback, lazy, Suspense } from 'react';
import type { GetServerSideProps } from 'next';

// Component imports
import AppTemplate from '@/components/templates/AppTemplate';
import ProjectsHeader from '@/components/organisms/ProjectsHeader';
import ProjectsGrid from '@/components/organisms/ProjectsGrid';
import ProjectsSidebar from '@/components/organisms/ProjectsSidebar';
import { NextPageWithLayout } from '@/pages/_app';

// Services and hooks
import { fetchProjectsServerSide } from '@/services/projectService';
import { useProjects } from '@/hooks/useProjects';
import type { Project } from '@/components/organisms/ProjectsGrid';

// Lazy load the dialog to improve initial page load
const CreateProjectDialog = lazy(
	() => import('@/components/molecules/CreateProjectDialog'),
);

interface ProjectsPageProps {
	initialProjects: Project[];
	error?: string;
}

const ProjectsPage: NextPageWithLayout<ProjectsPageProps> = ({
	initialProjects,
}) => {
	const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
	const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

	// Use the custom hook for project management
	const { projects, createProject } = useProjects({
		initialProjects,
	});

	const handleOpenDialog = useCallback(() => {
		setIsCreateDialogOpen(true);
	}, []);

	const handleCloseDialog = useCallback(() => {
		setIsCreateDialogOpen(false);
	}, []);

	const handleToggleSidebar = useCallback(() => {
		setIsSidebarCollapsed(!isSidebarCollapsed);
	}, [isSidebarCollapsed]);

	const handleProjectCreated = useCallback(
		async (projectData: { name: string; description: string }) => {
			const newProject = await createProject(projectData);
			if (newProject) {
				setIsCreateDialogOpen(false);
			}
			return newProject;
		},
		[createProject],
	);
	return (
		<div className='flex h-svh'>
			<ProjectsSidebar
				projects={projects}
				isCollapsed={isSidebarCollapsed}
				onToggle={handleToggleSidebar}
			/>
			<main className='flex-1 p-300 bg-background-base overflow-auto'>
				<ProjectsHeader onCreateProject={handleOpenDialog} />
				<ProjectsGrid projects={projects} />
			</main>

			{/* Create Project Dialog - Lazy loaded */}
			{isCreateDialogOpen && (
				<Suspense fallback={<div>Loading...</div>}>
					<CreateProjectDialog
						isOpen={isCreateDialogOpen}
						onClose={handleCloseDialog}
						onProjectCreated={handleProjectCreated}
					/>
				</Suspense>
			)}
		</div>
	);
};

ProjectsPage.getLayout = (page) => {
	return <AppTemplate>{page}</AppTemplate>;
};

// Server-side rendering for better performance
export const getServerSideProps: GetServerSideProps<ProjectsPageProps> = async (
	context,
) => {
	try {
		// Get access token from cookies if available
		const accessToken = context.req.cookies.accessToken;

		// Fetch projects on the server
		const result = await fetchProjectsServerSide(accessToken);

		return {
			props: {
				initialProjects: result.projects,
				...(result.error && { error: result.error }),
			},
		};
	} catch (error) {
		console.error('Error in getServerSideProps:', error);
		return {
			props: {
				initialProjects: [],
				error: 'Failed to load projects',
			},
		};
	}
};

export default ProjectsPage;
