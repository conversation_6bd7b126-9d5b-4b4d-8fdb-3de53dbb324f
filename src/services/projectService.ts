import { ProjectAPI } from '@/api/ProjectApi';
import {
	extractApiData,
	getApiErrorMessage,
	isApiSuccess,
} from '@/common/utils/apiResponse';
import type { Project } from '@/components/organisms/ProjectsGrid';

export interface ProjectsServiceResult {
	projects: Project[];
	error?: string;
}

/**
 * Server-side service for fetching projects
 * This runs on the server during SSR
 */
export async function fetchProjectsServerSide(
	accessToken?: string,
): Promise<ProjectsServiceResult> {
	try {
		// For SSR, if no access token is available, return empty projects
		// This prevents server-side API calls that would fail due to authentication
		if (!accessToken) {
			console.log(
				'No access token available for SSR, returning empty projects',
			);
			return {
				projects: [],
			};
		}

		// TODO: Implement server-side API call with proper authentication
		// For now, return empty projects to prevent SSR issues
		console.log(
			'SSR project fetching not yet implemented, returning empty projects',
		);
		return {
			projects: [],
		};

		// Future implementation would look like:
		// const api = new ProjectAPI();
		// const response = await api.getProjects();
		// ... handle response
	} catch (error) {
		console.error('Error fetching projects:', error);
		return {
			projects: [],
			error: error instanceof Error ? error.message : 'Unknown error occurred',
		};
	}
}

/**
 * Client-side service for creating projects
 * This runs in the browser
 */
export async function createProjectClientSide(projectData: {
	name: string;
	description: string;
}): Promise<{ project?: Project; error?: string }> {
	try {
		const api = new ProjectAPI();
		const response = await api.createProject(projectData);

		if (isApiSuccess(response)) {
			const project = extractApiData<Project>(response);
			if (project) {
				return { project };
			} else {
				return { error: 'Failed to create project: Invalid response data' };
			}
		} else {
			const errorMessage = getApiErrorMessage(response);
			return { error: errorMessage };
		}
	} catch (error) {
		console.error('Error creating project:', error);
		return {
			error:
				error instanceof Error
					? error.message
					: 'Network error or server unavailable',
		};
	}
}
