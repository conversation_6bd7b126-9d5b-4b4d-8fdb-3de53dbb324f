import { useState, useCallback, useEffect } from 'react';
import { createProjectClientSide } from '@/services/projectService';
import { ProjectAPI } from '@/api/ProjectApi';
import {
	extractApiData,
	getApiErrorMessage,
	isApiSuccess,
} from '@/common/utils/apiResponse';
import type { Project } from '@/components/organisms/ProjectsGrid';

interface UseProjectsProps {
	initialProjects: Project[];
}

interface UseProjectsReturn {
	projects: Project[];
	isCreating: boolean;
	error: string | null;
	createProject: (projectData: {
		name: string;
		description: string;
	}) => Promise<Project | null>;
	clearError: () => void;
}

export function useProjects({
	initialProjects,
}: UseProjectsProps): UseProjectsReturn {
	const [projects, setProjects] = useState<Project[]>(initialProjects);
	const [isCreating, setIsCreating] = useState(false);
	const [error, setError] = useState<string | null>(null);

	// Fetch projects on client-side if none were provided by SSR
	useEffect(() => {
		if (initialProjects.length === 0) {
			const fetchProjects = async () => {
				try {
					const api = new ProjectAPI();
					const response = await api.getProjects();
					if (isApiSuccess(response)) {
						const projectsData = extractApiData<Project[]>(response);
						setProjects(projectsData || []);
					} else {
						const errorMessage = getApiErrorMessage(response);
						console.error('Failed to fetch projects:', errorMessage);
						setError(errorMessage);
					}
				} catch (error) {
					console.error('Error fetching projects:', error);
					setError(
						error instanceof Error ? error.message : 'Failed to fetch projects',
					);
				}
			};
			fetchProjects();
		}
	}, [initialProjects.length]);

	const createProject = useCallback(
		async (projectData: { name: string; description: string }) => {
			setIsCreating(true);
			setError(null);

			const result = await createProjectClientSide(projectData);

			if (result.project) {
				setProjects((prev) => [result.project!, ...prev]);
				setIsCreating(false);
				return result.project;
			} else {
				setError(result.error || 'Failed to create project');
				setIsCreating(false);
				return null;
			}
		},
		[],
	);

	const clearError = useCallback(() => {
		setError(null);
	}, []);

	return {
		projects,
		isCreating,
		error,
		createProject,
		clearError,
	};
}
