import { useState, useCallback, lazy, Suspense } from 'react';

// Component imports
import AppTemplate from '@/components/templates/AppTemplate';
import ProjectsHeader from '@/components/organisms/ProjectsHeader';
import ProjectsGrid from '@/components/organisms/ProjectsGrid';
import ProjectsSidebar from '@/components/organisms/ProjectsSidebar';
import { NextPageWithLayout } from '@/pages/_app';

// Hooks
import { useProjects } from '@/hooks/useProjects';
import type { Project } from '@/components/organisms/ProjectsGrid';

// Lazy load the dialogs to improve initial page load
const CreateProjectDialog = lazy(
	() => import('@/components/molecules/CreateProjectDialog'),
);
const EditProjectDialog = lazy(
	() => import('@/components/molecules/EditProjectDialog'),
);
const DeleteProjectDialog = lazy(
	() => import('@/components/molecules/DeleteProjectDialog'),
);

const ProjectsPage: NextPageWithLayout = () => {
	const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
	const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
	const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
	const [selectedProject, setSelectedProject] = useState<Project | null>(null);

	// Use the custom hook for project management
	const {
		projects,
		createProject,
		updateProject,
		deleteProject,
		isLoading,
		canManageProjects,
	} = useProjects();

	const handleOpenDialog = useCallback(() => {
		setIsCreateDialogOpen(true);
	}, []);

	const handleCloseDialog = useCallback(() => {
		setIsCreateDialogOpen(false);
	}, []);

	const handleToggleSidebar = useCallback(() => {
		setIsSidebarCollapsed(!isSidebarCollapsed);
	}, [isSidebarCollapsed]);

	const handleProjectCreated = useCallback(
		async (projectData: { name: string; description: string }) => {
			const newProject = await createProject(projectData);
			if (newProject) {
				setIsCreateDialogOpen(false);
			}
			return newProject;
		},
		[createProject],
	);

	const handleEditProject = useCallback((project: Project) => {
		setSelectedProject(project);
		setIsEditDialogOpen(true);
	}, []);

	const handleDeleteProject = useCallback((project: Project) => {
		setSelectedProject(project);
		setIsDeleteDialogOpen(true);
	}, []);

	const handleProjectUpdated = useCallback(
		async (id: string, projectData: { name: string; description: string }) => {
			const updatedProject = await updateProject(id, projectData);
			if (updatedProject) {
				setIsEditDialogOpen(false);
				setSelectedProject(null);
			}
			return updatedProject;
		},
		[updateProject],
	);

	const handleProjectDeleted = useCallback(
		async (id: string) => {
			const success = await deleteProject(id);
			if (success) {
				setIsDeleteDialogOpen(false);
				setSelectedProject(null);
			}
			return success;
		},
		[deleteProject],
	);

	const handleCloseEditDialog = useCallback(() => {
		setIsEditDialogOpen(false);
		setSelectedProject(null);
	}, []);

	const handleCloseDeleteDialog = useCallback(() => {
		setIsDeleteDialogOpen(false);
		setSelectedProject(null);
	}, []);
	return (
		<div className='flex h-svh'>
			<ProjectsSidebar
				projects={projects}
				isCollapsed={isSidebarCollapsed}
				onToggle={handleToggleSidebar}
			/>
			<main className='flex-1 p-300 bg-background-base overflow-auto'>
				<ProjectsHeader
					onCreateProject={handleOpenDialog}
					canManageProjects={canManageProjects}
				/>
				<ProjectsGrid
					projects={projects}
					isLoading={isLoading}
					canManageProjects={canManageProjects}
					onEditProject={handleEditProject}
					onDeleteProject={handleDeleteProject}
				/>
			</main>

			{/* Create Project Dialog - Lazy loaded */}
			{isCreateDialogOpen && (
				<Suspense fallback={<div>Loading...</div>}>
					<CreateProjectDialog
						isOpen={isCreateDialogOpen}
						onClose={handleCloseDialog}
						onProjectCreated={handleProjectCreated}
					/>
				</Suspense>
			)}

			{/* Edit Project Dialog - Lazy loaded */}
			{isEditDialogOpen && (
				<Suspense fallback={<div>Loading...</div>}>
					<EditProjectDialog
						isOpen={isEditDialogOpen}
						onClose={handleCloseEditDialog}
						project={selectedProject}
						onProjectUpdated={handleProjectUpdated}
					/>
				</Suspense>
			)}

			{/* Delete Project Dialog - Lazy loaded */}
			{isDeleteDialogOpen && (
				<Suspense fallback={<div>Loading...</div>}>
					<DeleteProjectDialog
						isOpen={isDeleteDialogOpen}
						onClose={handleCloseDeleteDialog}
						project={selectedProject}
						onProjectDeleted={handleProjectDeleted}
					/>
				</Suspense>
			)}
		</div>
	);
};

ProjectsPage.getLayout = (page) => {
	return <AppTemplate>{page}</AppTemplate>;
};

export default ProjectsPage;
