import { useState } from 'react';
import Dialog from './Dialog';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Typo<PERSON> } from '@/components/atoms';
import type { Project } from '@/components/organisms/ProjectsGrid';

interface DeleteProjectDialogProps {
	isOpen: boolean;
	onClose: () => void;
	project: Project | null;
	onProjectDeleted?: (id: string) => Promise<boolean>;
}

const DeleteProjectDialog: React.FC<DeleteProjectDialogProps> = ({
	isOpen,
	onClose,
	project,
	onProjectDeleted,
}) => {
	const [isDeleting, setIsDeleting] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const handleDelete = async () => {
		if (!project) {
			setError('No project selected for deletion');
			return;
		}

		setError(null);
		setIsDeleting(true);

		try {
			if (onProjectDeleted) {
				const success = await onProjectDeleted(project.id);
				if (success) {
					// Dialog will be closed by parent component
				} else {
					setError('Failed to delete project');
				}
			}
		} catch (error) {
			console.error('Error deleting project:', error);
			if (error instanceof Error) {
				setError(`Failed to delete project: ${error.message}`);
			} else {
				setError(
					'Failed to delete project: Network error or server unavailable',
				);
			}
		} finally {
			setIsDeleting(false);
		}
	};

	const handleClose = () => {
		if (!isDeleting) {
			setError(null);
			onClose();
		}
	};

	return (
		<Dialog
			isOpen={isOpen}
			onClose={handleClose}
			title='Delete Project'
			description='This action cannot be undone.'
			size='md'>
			{error && (
				<Alert
					variant='negative'
					className='mb-300'>
					{error}
				</Alert>
			)}

			<div className='space-y-300'>
				<Typography
					variant='body'
					color='secondary'>
					Are you sure you want to delete the project{' '}
					<span className='font-medium text-text-primary'>
						"{project?.name}"
					</span>
					? This action cannot be undone and all project data will be
					permanently removed.
				</Typography>

				<div className='flex justify-end gap-150 pt-200 border-t border-border-base'>
					<Button
						type='button'
						variant='secondary'
						onClick={handleClose}
						disabled={isDeleting}>
						Cancel
					</Button>
					<Button
						type='button'
						variant='destructive'
						onClick={handleDelete}
						disabled={isDeleting}>
						{isDeleting && <ButtonLoader />}
						{isDeleting ? 'Deleting...' : 'Delete Project'}
					</Button>
				</div>
			</div>
		</Dialog>
	);
};

export default DeleteProjectDialog;
