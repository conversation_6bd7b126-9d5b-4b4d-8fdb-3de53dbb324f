import { useState, useCallback, useEffect } from 'react';
import { createProjectClientSide } from '@/services/projectService';
import { ProjectAPI } from '@/api/ProjectApi';
import { useAuthStore } from '@/providers/auth-store-provider';
import {
	extractApiData,
	getApiErrorMessage,
	isApiSuccess,
} from '@/common/utils/apiResponse';
import type { Project } from '@/components/organisms/ProjectsGrid';

interface UseProjectsProps {
	initialProjects: Project[];
}

interface UseProjectsReturn {
	projects: Project[];
	isCreating: boolean;
	isLoading: boolean;
	error: string | null;
	createProject: (projectData: {
		name: string;
		description: string;
	}) => Promise<Project | null>;
	clearError: () => void;
}

export function useProjects({
	initialProjects,
}: UseProjectsProps): UseProjectsReturn {
	const [projects, setProjects] = useState<Project[]>(initialProjects);
	const [isCreating, setIsCreating] = useState(false);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	// Get auth state
	const user = useAuthStore((state) => state.user);
	const isHydrated = useAuthStore((state) => state.isHydrated);

	// Fetch projects on client-side after auth is hydrated
	useEffect(() => {
		// Only fetch if:
		// 1. Auth store is hydrated (client-side)
		// 2. User is authenticated
		// 3. No initial projects (SSR returned empty)
		// 4. Not already loading
		// 5. No existing error
		if (
			isHydrated &&
			user &&
			initialProjects.length === 0 &&
			!isLoading &&
			!error
		) {
			const fetchProjects = async () => {
				setIsLoading(true);
				setError(null);

				try {
					const api = new ProjectAPI();
					const response = await api.getProjects();
					if (isApiSuccess(response)) {
						const projectsData = extractApiData<Project[]>(response);
						setProjects(projectsData || []);
					} else {
						const errorMessage = getApiErrorMessage(response);
						console.error('Failed to fetch projects:', errorMessage);
						setError(errorMessage);
					}
				} catch (error) {
					console.error('Error fetching projects:', error);
					setError(
						error instanceof Error ? error.message : 'Failed to fetch projects',
					);
				} finally {
					setIsLoading(false);
				}
			};

			fetchProjects();
		}

		// Clear projects if user logs out
		if (isHydrated && !user) {
			setProjects([]);
			setError(null);
		}
	}, [isHydrated, user, initialProjects.length, isLoading, error]);

	const createProject = useCallback(
		async (projectData: { name: string; description: string }) => {
			setIsCreating(true);
			setError(null);

			const result = await createProjectClientSide(projectData);

			if (result.project) {
				setProjects((prev) => [result.project!, ...prev]);
				setIsCreating(false);
				return result.project;
			} else {
				setError(result.error || 'Failed to create project');
				setIsCreating(false);
				return null;
			}
		},
		[],
	);

	const clearError = useCallback(() => {
		setError(null);
	}, []);

	return {
		projects,
		isCreating,
		isLoading,
		error,
		createProject,
		clearError,
	};
}
