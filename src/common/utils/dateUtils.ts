import {
	format,
	formatDistanceToNow,
	isValid,
	parseISO,
	isToday,
	isYesterday,
	isThisWeek,
	isThisYear,
} from 'date-fns';

/**
 * Utility functions for formatting dates using date-fns
 */

export const formatDate = (
	date: string | Date | null | undefined,
	formatString: string = 'MMM d, yyyy',
): string => {
	if (!date) return 'Not available';

	try {
		const dateObj = typeof date === 'string' ? parseISO(date) : date;

		if (!isValid(dateObj)) {
			return 'Invalid date';
		}

		return format(dateObj, formatString);
	} catch (error) {
		console.warn('Error formatting date:', error);
		return 'Invalid date';
	}
};

export const formatRelativeTime = (
	date: string | Date | null | undefined,
): string => {
	if (!date) return 'Not available';

	try {
		const dateObj = typeof date === 'string' ? parseISO(date) : date;

		if (!isValid(dateObj)) {
			return 'Invalid date';
		}

		return formatDistanceToNow(dateObj, { addSuffix: true });
	} catch (error) {
		console.warn('Error formatting relative time:', error);
		return 'Invalid date';
	}
};

export const formatSmartDate = (
	date: string | Date | null | undefined,
): string => {
	if (!date) return 'Not available';

	try {
		const dateObj = typeof date === 'string' ? parseISO(date) : date;

		if (!isValid(dateObj)) {
			return 'Invalid date';
		}

		if (isToday(dateObj)) {
			return `Today at ${format(dateObj, 'h:mm a')}`;
		}

		if (isYesterday(dateObj)) {
			return `Yesterday at ${format(dateObj, 'h:mm a')}`;
		}

		if (isThisWeek(dateObj)) {
			return format(dateObj, "EEEE 'at' h:mm a");
		}

		if (isThisYear(dateObj)) {
			return format(dateObj, "MMM d 'at' h:mm a");
		}

		return format(dateObj, 'MMM d, yyyy');
	} catch (error) {
		console.warn('Error formatting smart date:', error);
		return 'Invalid date';
	}
};

export const formatProjectDate = (
	date: string | Date | null | undefined,
): string => {
	if (!date) return 'Not available';

	try {
		const dateObj = typeof date === 'string' ? parseISO(date) : date;

		if (!isValid(dateObj)) {
			return 'Invalid date';
		}

		const now = new Date();
		const diffInDays = Math.floor(
			(now.getTime() - dateObj.getTime()) / (1000 * 60 * 60 * 24),
		);

		// Show relative time for dates within the last 7 days
		if (diffInDays <= 7) {
			return formatDistanceToNow(dateObj, { addSuffix: true });
		}

		// Show absolute date for older dates
		if (isThisYear(dateObj)) {
			return format(dateObj, 'MMM d');
		}

		return format(dateObj, 'MMM d, yyyy');
	} catch (error) {
		console.warn('Error formatting project date:', error);
		return 'Invalid date';
	}
};

export const formatFullDate = (
	date: string | Date | null | undefined,
): string => {
	if (!date) return 'Not available';

	try {
		const dateObj = typeof date === 'string' ? parseISO(date) : date;

		if (!isValid(dateObj)) {
			return 'Invalid date';
		}

		return format(dateObj, "MMMM d, yyyy 'at' h:mm a");
	} catch (error) {
		console.warn('Error formatting full date:', error);
		return 'Invalid date';
	}
};

export const formatInputDate = (
	date: string | Date | null | undefined,
): string => {
	if (!date) return '';

	try {
		const dateObj = typeof date === 'string' ? parseISO(date) : date;

		if (!isValid(dateObj)) {
			return '';
		}

		return format(dateObj, 'yyyy-MM-dd');
	} catch (error) {
		console.warn('Error formatting input date:', error);
		return '';
	}
};

export const isValidDate = (
	date: string | Date | null | undefined,
): boolean => {
	if (!date) return false;

	try {
		const dateObj = typeof date === 'string' ? parseISO(date) : date;
		return isValid(dateObj);
	} catch {
		return false;
	}
};
