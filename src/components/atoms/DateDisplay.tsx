import { memo } from 'react';
import Typography from './Typography';
import {
	formatDate,
	formatRelativeTime,
	formatSmartDate,
	formatProjectDate,
	formatFullDate,
	isValidDate,
} from '@/common/utils/dateUtils';
import type { TypographyProps } from './Typography';

export type DateFormatType =
	| 'default' // MMM d, yyyy
	| 'relative' // 2 hours ago
	| 'smart' // Today at 2:30 PM / Yesterday at 2:30 PM / Jan 15 at 2:30 PM
	| 'project' // Optimized for project cards
	| 'full' // January 15, 2024 at 2:30 PM
	| 'custom'; // Use custom format string

interface DateDisplayProps {
	date: string | Date | null | undefined;
	format?: DateFormatType;
	customFormat?: string; // Used when format is 'custom'
	fallback?: string; // Custom fallback text
	prefix?: string; // Text to show before the date
	suffix?: string; // Text to show after the date
	variant?: TypographyProps['variant'];
	color?: TypographyProps['color'];
	weight?: TypographyProps['weight'];
	className?: string;
	showTooltip?: boolean; // Show full date on hover
	title?: string; // Custom tooltip text
}

const DateDisplay = memo<DateDisplayProps>(
	({
		date,
		format: formatType = 'default',
		customFormat,
		fallback = 'Not available',
		prefix,
		suffix,
		variant = 'body-sm',
		color = 'secondary',
		weight,
		className,
		showTooltip = false,
		title,
	}) => {
		// Early return if date is invalid
		if (!isValidDate(date)) {
			return (
				<Typography
					variant={variant}
					color={color}
					weight={weight}
					className={className}>
					{prefix}
					{fallback}
					{suffix}
				</Typography>
			);
		}

		// Format the date based on the specified format type
		const getFormattedDate = (): string => {
			switch (formatType) {
				case 'relative':
					return formatRelativeTime(date);
				case 'smart':
					return formatSmartDate(date);
				case 'project':
					return formatProjectDate(date);
				case 'full':
					return formatFullDate(date);
				case 'custom':
					return customFormat
						? formatDate(date, customFormat)
						: formatDate(date);
				case 'default':
				default:
					return formatDate(date);
			}
		};

		const formattedDate = getFormattedDate();

		// Generate tooltip text if needed
		const tooltipText = showTooltip
			? title || formatFullDate(date)
			: title;

		return (
			<Typography
				variant={variant}
				color={color}
				weight={weight}
				className={className}
				title={tooltipText}>
				{prefix}
				{formattedDate}
				{suffix}
			</Typography>
		);
	},
);

DateDisplay.displayName = 'DateDisplay';

export default DateDisplay;
