import {
	useEffect,
	useState,
	useMemo,
	useCallback,
	lazy,
	Suspense,
} from 'react';
import { FolderIcon, PlusIcon } from '@heroicons/react/24/outline';

// Direct imports to reduce bundle size and compilation time
import ThemeToggle from '@/components/molecules/ThemeToggle';
import SideBar from '@/components/organisms/Sidebar';
import AppTemplate from '@/components/templates/AppTemplate';
import { NextPageWithLayout } from '@/pages/_app';

// Lazy load the dialog to improve initial page load
const CreateProjectDialog = lazy(
	() => import('@/components/molecules/CreateProjectDialog'),
);
import {
	Card,
	CardHeader,
	CardContent,
	CardFooter,
	Button,
	Typography,
	CardTitle,
	CardDescription,
} from '@/components/atoms';
import { ProjectAPI } from '@/api/ProjectApi';
import {
	extractApiData,
	getApiErrorMessage,
	isApiSuccess,
} from '@/common/utils/apiResponse';

// Define the Project interface
interface Project {
	id: string;
	name: string;
	description: string;
	createdAt?: string;
	updatedAt?: string;
}

const ProjectsPage: NextPageWithLayout = () => {
	const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
	const [projects, setProjects] = useState<Project[]>([]);
	const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

	useEffect(() => {
		const fetchProjects = async () => {
			try {
				const api = new ProjectAPI();
				const response = await api.getProjects();
				if (isApiSuccess(response)) {
					const projectsData = extractApiData<Project[]>(response);
					setProjects(projectsData || []);
				} else {
					const errorMessage = getApiErrorMessage(response);
					console.error('Failed to fetch projects:', errorMessage);
				}
			} catch (error) {
				console.error('Error fetching projects:', error);
			}
		};
		fetchProjects();
	}, []);

	const handleProjectCreated = useCallback((newProject: Project) => {
		setProjects((prevProjects) => [newProject, ...prevProjects]);
	}, []);

	const handleOpenDialog = useCallback(() => {
		setIsCreateDialogOpen(true);
	}, []);

	const handleCloseDialog = useCallback(() => {
		setIsCreateDialogOpen(false);
	}, []);

	const handleToggleSidebar = useCallback(() => {
		setIsSidebarCollapsed(!isSidebarCollapsed);
	}, [isSidebarCollapsed]);

	// Memoize sidebar items to prevent recreation on every render
	const sidebarItems = useMemo(
		() =>
			projects.map((project) => ({
				label: project.name,
				icon: <FolderIcon className='w-4 h-4' />,
				onClick: () => console.log('Project clicked:', project.id),
			})),
		[projects],
	);

	// Memoize sidebar sections
	const sidebarSections = useMemo(
		() => [
			{
				title: 'My Projects',
				items: sidebarItems,
			},
		],
		[sidebarItems],
	);

	// Memoize project cards to prevent unnecessary re-renders
	const projectCards = useMemo(
		() =>
			projects.map((project) => (
				<Card
					key={project.id}
					variant='outlined'
					size='medium'
					interactive
					className='h-full'>
					<CardHeader>
						<CardTitle>{project.name}</CardTitle>
						<CardDescription>{project.description}</CardDescription>
					</CardHeader>
					<CardContent>
						<Typography
							variant='body'
							color='secondary'>
							Created: {project.createdAt}
						</Typography>
						<Typography
							variant='body'
							color='secondary'>
							Updated: {project.updatedAt}
						</Typography>
					</CardContent>
					<CardFooter>
						<Button
							variant='primary'
							size='sm'>
							View
						</Button>
					</CardFooter>
				</Card>
			)),
		[projects],
	);
	return (
		<div className='flex h-svh'>
			<SideBar
				title='Projects'
				sections={sidebarSections}
				isCollapsed={isSidebarCollapsed}
				onToggle={handleToggleSidebar}
			/>
			<main className='flex-1 p-300 bg-background-base overflow-auto'>
				{/* Header Section */}
				<div className='mb-400'>
					<div className='flex justify-between items-center mb-200'>
						<div>
							<Typography
								variant='h1'
								responsive>
								Projects
							</Typography>
							<Typography
								variant='body-lg'
								color='secondary'
								className='mt-2'>
								Manage and track your creative projects with our enhanced design
								system.
							</Typography>
						</div>
						<div className='flex items-center gap-150'>
							<ThemeToggle />
							<Button
								variant='primary'
								size='md'
								onClick={handleOpenDialog}>
								<PlusIcon className='w-4 h-4 mr-2' />
								New Project
							</Button>
						</div>
					</div>
				</div>

				{/* Projects Grid */}
				<div className='grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-300 mb-400'>
					{projectCards}
				</div>
			</main>

			{/* Create Project Dialog - Lazy loaded */}
			{isCreateDialogOpen && (
				<Suspense fallback={<div>Loading...</div>}>
					<CreateProjectDialog
						isOpen={isCreateDialogOpen}
						onClose={handleCloseDialog}
						onProjectCreated={handleProjectCreated}
					/>
				</Suspense>
			)}
		</div>
	);
};

ProjectsPage.getLayout = (page) => {
	return <AppTemplate>{page}</AppTemplate>;
};

export default ProjectsPage;
