import { forwardRef } from 'react';
import { clsx } from 'clsx';

interface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
	className?: string;
	children: React.ReactNode;
	variant?:
		| 'neutral'
		| 'accent'
		| 'positive'
		| 'negative'
		| 'notice'
		| 'informative';
	size?: 'small' | 'medium' | 'large';
	emphasis?: 'subtle' | 'strong' | 'outline';
	icon?: React.ReactNode;
	removable?: boolean;
	onRemove?: () => void;
}

const badgeVariants = {
	neutral: {
		subtle: [
			'text-text-primary',
			'bg-background-layer-1',
			'border-border-base',
		],
		strong: ['text-text-inverse', 'bg-text-primary', 'border-text-primary'],
		outline: ['text-text-primary', 'bg-transparent', 'border-border-strong'],
	},
	accent: {
		subtle: [
			'text-accent-content-default',
			'bg-accent-background-default/10',
			'border-accent-border-default/20',
		],
		strong: [
			'text-text-inverse',
			'bg-accent-background-default',
			'border-accent-background-default',
		],
		outline: [
			'text-accent-content-default',
			'bg-transparent',
			'border-accent-border-default',
		],
	},
	positive: {
		subtle: [
			'text-positive-content',
			'bg-positive-background',
			'border-positive-border/30',
		],
		strong: [
			'text-text-inverse',
			'bg-positive-content',
			'border-positive-content',
		],
		outline: [
			'text-positive-content',
			'bg-transparent',
			'border-positive-border',
		],
	},
	negative: {
		subtle: [
			'text-negative-content',
			'bg-negative-background',
			'border-negative-border/30',
		],
		strong: [
			'text-text-inverse',
			'bg-negative-content',
			'border-negative-content',
		],
		outline: [
			'text-negative-content',
			'bg-transparent',
			'border-negative-border',
		],
	},
	notice: {
		subtle: [
			'text-notice-content',
			'bg-notice-background',
			'border-notice-border/30',
		],
		strong: ['text-text-inverse', 'bg-notice-content', 'border-notice-content'],
		outline: ['text-notice-content', 'bg-transparent', 'border-notice-border'],
	},
	informative: {
		subtle: [
			'text-informative-content',
			'bg-informative-background',
			'border-informative-border/30',
		],
		strong: [
			'text-text-inverse',
			'bg-informative-content',
			'border-informative-content',
		],
		outline: [
			'text-informative-content',
			'bg-transparent',
			'border-informative-border',
		],
	},
};

const badgeSizes = {
	small: {
		base: 'px-100 py-25 text-responsive-xs',
		icon: 'w-3 h-3',
		remove: 'w-3 h-3 ml-50',
	},
	medium: {
		base: 'px-150 py-75 text-responsive-sm',
		icon: 'w-3.5 h-3.5',
		remove: 'w-3.5 h-3.5 ml-100',
	},
	large: {
		base: 'px-200 py-100 text-responsive-base',
		icon: 'w-4 h-4',
		remove: 'w-4 h-4 ml-150',
	},
};

const Badge = forwardRef<HTMLSpanElement, BadgeProps>(
	(
		{
			className,
			children,
			variant = 'neutral',
			size = 'medium',
			emphasis = 'subtle',
			icon,
			removable = false,
			onRemove,
			...props
		},
		ref,
	) => {
		const handleRemove = (e: React.MouseEvent) => {
			e.stopPropagation();
			onRemove?.();
		};

		return (
			<span
				ref={ref}
				className={clsx(
					// Base styles
					'inline-flex text-xs items-center justify-center',
					'font-medium rounded-badge border',
					'transition-all duration-200 ease-in-out',
					'whitespace-nowrap',
					// Size styles
					badgeSizes[size].base,
					// Variant and emphasis styles
					badgeVariants[variant][emphasis],
					className,
				)}
				{...props}>
				{icon && (
					<span className={clsx('flex-shrink-0', badgeSizes[size].icon)}>
						{icon}
					</span>
				)}

				<span className={clsx(icon && 'ml-1')}>{children}</span>

				{removable && (
					<button
						type='button'
						onClick={handleRemove}
						className={clsx(
							'flex-shrink-0 rounded',
							'hover:bg-black/10 dark:hover:bg-white/10',
							'focus:outline-none focus:ring-1 focus:ring-current',
							'transition-colors duration-150',
							badgeSizes[size].remove,
						)}
						aria-label='Remove badge'>
						<svg
							className='w-full h-full'
							fill='none'
							stroke='currentColor'
							viewBox='0 0 24 24'>
							<path
								strokeLinecap='round'
								strokeLinejoin='round'
								strokeWidth={2}
								d='M6 18L18 6M6 6l12 12'
							/>
						</svg>
					</button>
				)}
			</span>
		);
	},
);

Badge.displayName = 'Badge';

export default Badge;
export type { BadgeProps };
