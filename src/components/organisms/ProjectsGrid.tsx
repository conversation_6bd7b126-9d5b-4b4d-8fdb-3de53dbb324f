import { memo } from 'react';
import { useRouter } from 'next/router';
import { PencilIcon, TrashIcon } from '@heroicons/react/24/outline';
import {
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>nt,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	Typo<PERSON>,
	Card<PERSON>itle,
	CardDescription,
	Badge,
	DateDisplay,
} from '@/components/atoms';
import ProjectCardSkeleton from '@/components/molecules/ProjectCardSkeleton';

export type ProjectStatus =
	| 'DRAFT'
	| 'ACTIVE'
	| 'COMPLETED'
	| 'ARCHIVED'
	| 'ON_HOLD';

export interface Project {
	id: string;
	name: string;
	description: string;
	status: ProjectStatus;
	createdAt?: string;
	updatedAt?: string;
}

interface ProjectsGridProps {
	projects: Project[];
	isLoading?: boolean;
	canManageProjects?: boolean;
	onEditProject?: (project: Project) => void;
	onDeleteProject?: (project: Project) => void;
}

// Helper function to get badge variant and text for project status
export const getStatusBadge = (status: ProjectStatus) => {
	switch (status) {
		case 'DRAFT':
			return {
				variant: 'neutral' as const,
				text: 'Draft',
			};
		case 'ACTIVE':
			return {
				variant: 'positive' as const,
				text: 'Active',
			};
		case 'COMPLETED':
			return {
				variant: 'accent' as const,
				text: 'Completed',
			};
		case 'ARCHIVED':
			return {
				variant: 'informative' as const,
				text: 'Archived',
			};
		case 'ON_HOLD':
			return {
				variant: 'notice' as const,
				text: 'On Hold',
			};
		default:
			return {
				variant: 'neutral' as const,
				text: 'Unknown',
			};
	}
};

const ProjectCard = memo<{
	project: Project;
	canManageProjects?: boolean;
	onEditProject?: (project: Project) => void;
	onDeleteProject?: (project: Project) => void;
}>(({ project, canManageProjects, onEditProject, onDeleteProject }) => {
	const router = useRouter();
	const statusBadge = getStatusBadge(project.status);

	const handleViewProject = () => {
		router.push(`/projects/${project.id}`);
	};

	return (
		<Card
			variant='outlined'
			size='medium'
			interactive
			className='h-full'>
			<CardHeader>
				<div className='flex items-start justify-between mb-100 gap-100'>
					<CardTitle className='flex-1 min-w-0 truncate'>
						{project.name}
					</CardTitle>
					<Badge
						variant={statusBadge.variant}
						size='small'
						emphasis='subtle'
						className='flex-shrink-0'>
						{statusBadge.text}
					</Badge>
				</div>
				<CardDescription className='line-clamp-2 sm:line-clamp-3'>
					{project.description}
				</CardDescription>
			</CardHeader>
			<CardContent>
				<DateDisplay
					date={project.createdAt}
					format='project'
					prefix='Created: '
					variant='body-sm'
					color='secondary'
					showTooltip
				/>
				<DateDisplay
					date={project.updatedAt}
					format='project'
					prefix='Updated: '
					variant='body-sm'
					color='secondary'
					showTooltip
				/>
			</CardContent>
			<CardFooter>
				<div className='flex flex-wrap gap-100 sm:gap-150'>
					<Button
						variant='primary'
						size='sm'
						onClick={handleViewProject}
						className='flex-1 sm:flex-none min-w-0'>
						View
					</Button>
					{canManageProjects && (
						<>
							<Button
								variant='secondary'
								size='sm'
								onClick={() => onEditProject?.(project)}
								aria-label={`Edit ${project.name}`}
								className='flex-shrink-0'>
								<PencilIcon className='w-4 h-4' />
								<span className='sr-only'>Edit</span>
							</Button>
							<Button
								variant='destructive'
								size='sm'
								onClick={() => onDeleteProject?.(project)}
								aria-label={`Delete ${project.name}`}
								className='flex-shrink-0'>
								<TrashIcon className='w-4 h-4' />
								<span className='sr-only'>Delete</span>
							</Button>
						</>
					)}
				</div>
			</CardFooter>
		</Card>
	);
});

ProjectCard.displayName = 'ProjectCard';

const ProjectsGrid = memo<ProjectsGridProps>(
	({
		projects,
		isLoading = false,
		canManageProjects = false,
		onEditProject,
		onDeleteProject,
	}) => {
		if (isLoading) {
			// Show skeleton cards while loading for better UX
			return (
				<div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-200 sm:gap-300 mb-400'>
					{Array.from({ length: 6 }).map((_, index) => (
						<ProjectCardSkeleton key={index} />
					))}
				</div>
			);
		}

		if (projects.length === 0) {
			return (
				<div className='flex flex-col items-center justify-center py-8 sm:py-12 px-200'>
					<Typography
						variant='h3'
						color='secondary'
						className='mb-2 text-center'>
						No projects yet
					</Typography>
					<Typography
						variant='body'
						color='tertiary'
						className='text-center max-w-md'>
						Create your first project to get started
					</Typography>
				</div>
			);
		}

		return (
			<div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-200 sm:gap-300 mb-400'>
				{projects.map((project) => (
					<ProjectCard
						key={project.id}
						project={project}
						canManageProjects={canManageProjects}
						onEditProject={onEditProject}
						onDeleteProject={onDeleteProject}
					/>
				))}
			</div>
		);
	},
);

ProjectsGrid.displayName = 'ProjectsGrid';

export default ProjectsGrid;
