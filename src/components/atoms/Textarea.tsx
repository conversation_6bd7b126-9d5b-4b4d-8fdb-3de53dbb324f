import { Textarea as HeadlessTextarea } from '@headlessui/react';
import { forwardRef } from 'react';
import { clsx } from 'clsx';

interface TextareaProps
	extends React.ComponentProps<typeof HeadlessTextarea>,
		Omit<
			React.TextareaHTMLAttributes<HTMLTextAreaElement>,
			keyof React.ComponentProps<typeof HeadlessTextarea> | 'size'
		> {
	className?: string;
	textareaSize?: 'small' | 'medium' | 'large';
	variant?: 'default' | 'error';
}

const textareaSizes = {
	small: 'px-150 py-75 text-responsive-sm min-h-[5rem]',
	medium: 'px-200 py-100 text-responsive-base min-h-[6rem]',
	large: 'px-300 py-150 text-responsive-lg min-h-[8rem]',
};

const textareaVariants = {
	default: [
		'bg-surface-base text-text-primary',
		'border border-border-base',
		'focus:border-accent-border-focus focus:ring-1 focus:ring-accent-border-focus',
	],
	error: [
		'bg-surface-base text-text-primary',
		'border border-negative-border',
		'focus:border-negative-border focus:ring-1 focus:ring-negative-border',
	],
};

const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(
	(
		{ className, textareaSize = 'medium', variant = 'default', ...props },
		ref,
	) => {
		return (
			<HeadlessTextarea
				ref={ref}
				className={clsx(
					// Base styles
					'block w-full rounded-input resize-vertical',
					'transition-all duration-medium ease-in-out',
					'focus:outline-none',
					// Placeholder styles
					'placeholder:text-text-tertiary',
					// Disabled styles
					'disabled:cursor-not-allowed disabled:opacity-50',
					'disabled:bg-background-layer-1',
					// Size styles
					textareaSizes[textareaSize],
					// Variant styles
					textareaVariants[variant],
					className,
				)}
				{...props}
			/>
		);
	},
);

Textarea.displayName = 'Textarea';
export default Textarea;
