import { memo } from 'react';
import { PlusIcon } from '@heroicons/react/24/outline';
import { Button, Typography } from '@/components/atoms';
import ThemeToggle from '@/components/molecules/ThemeToggle';

interface ProjectsHeaderProps {
	onCreateProject: () => void;
}

const ProjectsHeader = memo<ProjectsHeaderProps>(({ onCreateProject }) => {
	return (
		<div className='mb-400'>
			<div className='flex justify-between items-center mb-200'>
				<div>
					<Typography
						variant='h1'
						responsive>
						Projects
					</Typography>
					<Typography
						variant='body-lg'
						color='secondary'
						className='mt-2'>
						Manage and track your creative projects with our enhanced design
						system.
					</Typography>
				</div>
				<div className='flex items-center gap-150'>
					<ThemeToggle />
					<Button
						variant='primary'
						size='md'
						onClick={onCreateProject}>
						<PlusIcon className='w-4 h-4 mr-2' />
						New Project
					</Button>
				</div>
			</div>
		</div>
	);
});

ProjectsHeader.displayName = 'ProjectsHeader';

export default ProjectsHeader;
