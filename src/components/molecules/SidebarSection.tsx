import { clsx } from 'clsx';
import { Typography } from '../atoms';
import SidebarItem, { SidebarItemProps } from './SidebarItem';

export interface SidebarSectionProps {
	title: string;
	items?: SidebarItemProps[];
	isCollapsed?: boolean;
}

const SidebarSection: React.FC<SidebarSectionProps> = ({
	title,
	items,
	isCollapsed = false,
}) => {
	if (!items || items.length === 0) {
		return null;
	}

	return (
		<section className={clsx('px-200 py-200')}>
			{!isCollapsed && title && (
				<Typography
					variant='caption'
					color='tertiary'
					weight='medium'
					className='mb-150 px-150 tracking-tight'>
					{title}
				</Typography>
			)}
			<nav className='space-y-75'>
				{items.map((item, index) => (
					<SidebarItem
						key={item.label || index}
						isCollapsed={isCollapsed}
						{...item}
					/>
				))}
			</nav>
		</section>
	);
};

SidebarSection.displayName = 'SidebarSection';
export default SidebarSection;
