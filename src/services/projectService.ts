import { ProjectAPI } from '@/api/ProjectApi';
import {
	extractApiData,
	getApiErrorMessage,
	isApiSuccess,
} from '@/common/utils/apiResponse';
import type {
	Project,
	ProjectStatus,
} from '@/components/organisms/ProjectsGrid';

/**
 * Client-side service for creating projects
 * This runs in the browser
 */
export async function createProjectClientSide(projectData: {
	name: string;
	description: string;
	status?: ProjectStatus;
}): Promise<{ project?: Project; error?: string }> {
	try {
		const api = new ProjectAPI();
		// Note: API only accepts name and description, status is handled by backend
		const response = await api.createProject({
			name: projectData.name,
			description: projectData.description,
		});

		if (isApiSuccess(response)) {
			const project = extractApiData<Project>(response);
			if (project) {
				return { project };
			} else {
				return { error: 'Failed to create project: Invalid response data' };
			}
		} else {
			const errorMessage = getApiErrorMessage(response);
			return { error: errorMessage };
		}
	} catch (error) {
		console.error('Error creating project:', error);
		return {
			error:
				error instanceof Error
					? error.message
					: 'Network error or server unavailable',
		};
	}
}

/**
 * Client-side service for updating projects
 * This runs in the browser
 */
export async function updateProjectClientSide(
	id: string,
	projectData: {
		name: string;
		description: string;
		status?: ProjectStatus;
	},
): Promise<{ project?: Project; error?: string }> {
	try {
		const api = new ProjectAPI();
		// Note: API only accepts name and description, status is handled by backend
		const response = await api.updateProject(id, {
			name: projectData.name,
			description: projectData.description,
		});

		if (isApiSuccess(response)) {
			const project = extractApiData<Project>(response);
			if (project) {
				return { project };
			} else {
				return { error: 'Failed to update project: Invalid response data' };
			}
		} else {
			const errorMessage = getApiErrorMessage(response);
			return { error: errorMessage };
		}
	} catch (error) {
		console.error('Error updating project:', error);
		return {
			error:
				error instanceof Error
					? error.message
					: 'Network error or server unavailable',
		};
	}
}

/**
 * Client-side service for deleting projects
 * This runs in the browser
 */
export async function deleteProjectClientSide(
	id: string,
): Promise<{ success?: boolean; error?: string }> {
	try {
		const api = new ProjectAPI();
		const response = await api.deleteProject(id);

		if (isApiSuccess(response)) {
			return { success: true };
		} else {
			const errorMessage = getApiErrorMessage(response);
			return { error: errorMessage };
		}
	} catch (error) {
		console.error('Error deleting project:', error);
		return {
			error:
				error instanceof Error
					? error.message
					: 'Network error or server unavailable',
		};
	}
}
