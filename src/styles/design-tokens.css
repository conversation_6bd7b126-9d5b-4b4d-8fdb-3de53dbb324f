/**
 * Design Token Values
 * 
 * This file contains ONLY the raw design token values for light mode, dark mode, and system preferences.
 * These values are used by the @theme directive in globals.css to create utility classes.
 * 
 * To change the app's appearance, modify the values here and they will automatically
 * propagate to all utility classes and components.
 */

/* ===== LIGHT MODE (DEFAULT) ===== */
:root {
  /* Base Color Palette - OKCH colors for better perceptual uniformity */
  --gray-25: oklch(99% 0.002 247);
  --gray-50: oklch(97% 0.003 247);
  --gray-75: oklch(95% 0.004 247);
  --gray-100: oklch(92% 0.005 247);
  --gray-200: oklch(87% 0.007 247);
  --gray-300: oklch(78% 0.012 247);
  --gray-400: oklch(65% 0.015 247);
  --gray-500: oklch(52% 0.012 247);
  --gray-600: oklch(42% 0.010 247);
  --gray-700: oklch(32% 0.008 247);
  --gray-800: oklch(22% 0.006 247);
  --gray-900: oklch(15% 0.004 247);
  --gray-950: oklch(10% 0.002 247);

  --blue-100: oklch(95% 0.025 250);
  --blue-200: oklch(87% 0.045 250);
  --blue-300: oklch(75% 0.075 250);
  --blue-400: oklch(62% 0.125 250);
  --blue-500: oklch(55% 0.155 250);
  --blue-600: oklch(45% 0.135 250);
  --blue-700: oklch(35% 0.115 250);
  --blue-800: oklch(25% 0.095 250);
  --blue-900: oklch(15% 0.075 250);

  --green-100: oklch(94% 0.035 145);
  --green-200: oklch(85% 0.065 145);
  --green-300: oklch(75% 0.095 145);
  --green-400: oklch(65% 0.115 145);
  --green-500: oklch(55% 0.125 145);
  --green-600: oklch(45% 0.105 145);
  --green-700: oklch(35% 0.085 145);
  --green-800: oklch(25% 0.065 145);
  --green-900: oklch(15% 0.045 145);

  --red-100: oklch(94% 0.025 25);
  --red-200: oklch(82% 0.055 25);
  --red-300: oklch(70% 0.085 25);
  --red-400: oklch(62% 0.125 25);
  --red-500: oklch(52% 0.145 25);
  --red-600: oklch(42% 0.125 25);
  --red-700: oklch(32% 0.105 25);
  --red-800: oklch(22% 0.085 25);
  --red-900: oklch(15% 0.065 25);

  --orange-100: oklch(96% 0.025 65);
  --orange-200: oklch(87% 0.055 65);
  --orange-300: oklch(75% 0.095 65);
  --orange-400: oklch(68% 0.125 65);
  --orange-500: oklch(62% 0.145 65);
  --orange-600: oklch(52% 0.125 65);
  --orange-700: oklch(42% 0.105 65);
  --orange-800: oklch(32% 0.085 65);
  --orange-900: oklch(22% 0.065 65);

  /* Primary Brand Color - Change this to update the entire app's primary color */
  --primary-hue: 155.67;
  --primary-saturation: 0.1706;
  --primary-lightness: 60%;

  /* Design Token Scales */
  --radius-scale-small: 0.5rem;
  --radius-scale-medium: 1rem;
  --radius-scale-large: 1.5rem;
  --radius-scale-xl: 2rem;

  --spacing-scale-xs: 0.125rem;
  --spacing-scale-sm: 0.25rem;
  --spacing-scale-base: 0.5rem;
  --spacing-scale-md: 0.75rem;
  --spacing-scale-lg: 1rem;
  --spacing-scale-xl: 1.5rem;
  --spacing-scale-2xl: 2rem;

  --text-scale-xs: 0.75rem;
  --text-scale-sm: 0.875rem;
  --text-scale-base: 1rem;
  --text-scale-lg: 1.125rem;
  --text-scale-xl: 1.25rem;
  --text-scale-2xl: 1.5rem;
  --text-scale-3xl: 1.875rem;
  --text-scale-4xl: 2.25rem;

  --shadow-scale-subtle: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-scale-small: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-scale-medium: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-scale-large: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* Semantic Token Assignments - Light Mode */
  --background-base: var(--gray-25);
  --background-layer-1: var(--gray-50);
  --background-layer-2: var(--gray-75);

  --surface-base: oklch(100% 0 0);
  --surface-raised: oklch(100% 0 0);
  --surface-overlay: oklch(100% 0 0);

  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-700);
  --text-tertiary: var(--gray-500);
  --text-disabled: var(--gray-400);
  --text-inverse: oklch(100% 0 0);

  --border-base: var(--gray-200);
  --border-strong: var(--gray-300);
  --border-subtle: var(--gray-100);

  /* Dynamic Primary Colors */
  --accent-content-default: oklch(var(--primary-lightness) var(--primary-saturation) var(--primary-hue));
  --accent-content-hover: oklch(calc(var(--primary-lightness) - 5%) var(--primary-saturation) var(--primary-hue));
  --accent-content-down: oklch(calc(var(--primary-lightness) - 10%) var(--primary-saturation) var(--primary-hue));
  --accent-content-focus: oklch(var(--primary-lightness) var(--primary-saturation) var(--primary-hue));
  --accent-background-default: oklch(var(--primary-lightness) var(--primary-saturation) var(--primary-hue));
  --accent-background-hover: oklch(calc(var(--primary-lightness) - 5%) var(--primary-saturation) var(--primary-hue));
  --accent-background-down: oklch(calc(var(--primary-lightness) - 10%) var(--primary-saturation) var(--primary-hue));
  --accent-border-default: oklch(var(--primary-lightness) var(--primary-saturation) var(--primary-hue));
  --accent-border-hover: oklch(calc(var(--primary-lightness) - 5%) var(--primary-saturation) var(--primary-hue));
  --accent-border-focus: oklch(var(--primary-lightness) var(--primary-saturation) var(--primary-hue));

  /* Semantic Status Colors */
  --positive-content: var(--green-600);
  --positive-background: var(--green-100);
  --positive-border: var(--green-300);
  --negative-content: var(--red-600);
  --negative-background: var(--red-100);
  --negative-border: var(--red-300);
  --notice-content: var(--orange-600);
  --notice-background: var(--orange-100);
  --notice-border: var(--orange-300);
  --informative-content: var(--blue-600);
  --informative-background: var(--blue-100);
  --informative-border: var(--blue-300);

  /* Component-specific tokens */
  --radius-default: var(--radius-scale-small);
  --radius-button: var(--radius-scale-small);
  --radius-card: var(--radius-scale-small);
  --radius-input: var(--radius-scale-small);
  --radius-badge: var(--radius-scale-small);
  --radius-progress: var(--radius-scale-small);

  --spacing-component-xs: var(--spacing-scale-xs);
  --spacing-component-sm: var(--spacing-scale-sm);
  --spacing-component-base: var(--spacing-scale-base);
  --spacing-component-md: var(--spacing-scale-md);
  --spacing-component-lg: var(--spacing-scale-lg);
  --spacing-component-xl: var(--spacing-scale-xl);
  --spacing-component-2xl: var(--spacing-scale-2xl);

  --text-component-xs: var(--text-scale-xs);
  --text-component-sm: var(--text-scale-sm);
  --text-component-base: var(--text-scale-base);
  --text-component-lg: var(--text-scale-lg);
  --text-component-xl: var(--text-scale-xl);
  --text-component-2xl: var(--text-scale-2xl);
  --text-component-3xl: var(--text-scale-3xl);
  --text-component-4xl: var(--text-scale-4xl);

  --shadow-component-subtle: var(--shadow-scale-subtle);
  --shadow-component-small: var(--shadow-scale-small);
  --shadow-component-medium: var(--shadow-scale-medium);
  --shadow-component-large: var(--shadow-scale-large);

  /* Component heights */
  --height-button-small: 2rem;
  --height-button-medium: 2.5rem;
  --height-button-large: 3rem;
  --height-input-small: 2rem;
  --height-input-medium: 2.5rem;
  --height-input-large: 3rem;

  /* Animation durations */
  --duration-fast: 150ms;
  --duration-medium: 200ms;
  --duration-slow: 300ms;
}

/* ===== DARK MODE ===== */
.dark {
  /* Semantic Token Assignments - Dark Mode */
  --background-base: var(--gray-950);
  --background-layer-1: var(--gray-900);
  --background-layer-2: var(--gray-800);

  --surface-base: var(--gray-900);
  --surface-raised: var(--gray-800);
  --surface-overlay: var(--gray-800);

  --text-primary: var(--gray-50);
  --text-secondary: var(--gray-300);
  --text-tertiary: var(--gray-400);
  --text-disabled: var(--gray-500);
  --text-inverse: var(--gray-900);

  --border-base: var(--gray-700);
  --border-strong: var(--gray-600);
  --border-subtle: var(--gray-800);

  /* Dynamic Primary Colors - Dark Mode */
  --accent-content-default: oklch(calc(var(--primary-lightness) + 10%) var(--primary-saturation) var(--primary-hue));
  --accent-content-hover: oklch(calc(var(--primary-lightness) + 15%) var(--primary-saturation) var(--primary-hue));
  --accent-content-down: oklch(calc(var(--primary-lightness) + 5%) var(--primary-saturation) var(--primary-hue));
  --accent-content-focus: oklch(calc(var(--primary-lightness) + 10%) var(--primary-saturation) var(--primary-hue));
  --accent-background-default: oklch(var(--primary-lightness) var(--primary-saturation) var(--primary-hue));
  --accent-background-hover: oklch(calc(var(--primary-lightness) + 5%) var(--primary-saturation) var(--primary-hue));
  --accent-background-down: oklch(calc(var(--primary-lightness) - 5%) var(--primary-saturation) var(--primary-hue));
  --accent-border-default: oklch(calc(var(--primary-lightness) + 5%) var(--primary-saturation) var(--primary-hue));
  --accent-border-hover: oklch(calc(var(--primary-lightness) + 10%) var(--primary-saturation) var(--primary-hue));
  --accent-border-focus: oklch(calc(var(--primary-lightness) + 10%) var(--primary-saturation) var(--primary-hue));

  /* Semantic Status Colors - Dark Mode */
  --positive-content: var(--green-400);
  --positive-background: var(--green-900);
  --positive-border: var(--green-700);
  --negative-content: var(--red-400);
  --negative-background: var(--red-900);
  --negative-border: var(--red-700);
  --notice-content: var(--orange-400);
  --notice-background: var(--orange-900);
  --notice-border: var(--orange-700);
  --informative-content: var(--blue-400);
  --informative-background: var(--blue-900);
  --informative-border: var(--blue-700);
}

/* ===== SYSTEM PREFERENCE DARK MODE ===== */
@media (prefers-color-scheme: dark) {
  :root:not([class]) {
    /* Semantic Token Assignments - System Dark Mode */
    --background-base: var(--gray-950);
    --background-layer-1: var(--gray-900);
    --background-layer-2: var(--gray-800);

    --surface-base: var(--gray-900);
    --surface-raised: var(--gray-800);
    --surface-overlay: var(--gray-800);

    --text-primary: var(--gray-50);
    --text-secondary: var(--gray-300);
    --text-tertiary: var(--gray-400);
    --text-disabled: var(--gray-500);
    --text-inverse: var(--gray-900);

    --border-base: var(--gray-700);
    --border-strong: var(--gray-600);
    --border-subtle: var(--gray-800);

    /* Dynamic Primary Colors - System Dark Mode */
    --accent-content-default: oklch(calc(var(--primary-lightness) + 10%) var(--primary-saturation) var(--primary-hue));
    --accent-content-hover: oklch(calc(var(--primary-lightness) + 15%) var(--primary-saturation) var(--primary-hue));
    --accent-content-down: oklch(calc(var(--primary-lightness) + 5%) var(--primary-saturation) var(--primary-hue));
    --accent-content-focus: oklch(calc(var(--primary-lightness) + 10%) var(--primary-saturation) var(--primary-hue));
    --accent-background-default: oklch(var(--primary-lightness) var(--primary-saturation) var(--primary-hue));
    --accent-background-hover: oklch(calc(var(--primary-lightness) + 5%) var(--primary-saturation) var(--primary-hue));
    --accent-background-down: oklch(calc(var(--primary-lightness) - 5%) var(--primary-saturation) var(--primary-hue));
    --accent-border-default: oklch(calc(var(--primary-lightness) + 5%) var(--primary-saturation) var(--primary-hue));
    --accent-border-hover: oklch(calc(var(--primary-lightness) + 10%) var(--primary-saturation) var(--primary-hue));
    --accent-border-focus: oklch(calc(var(--primary-lightness) + 10%) var(--primary-saturation) var(--primary-hue));

    /* Semantic Status Colors - System Dark Mode */
    --positive-content: var(--green-400);
    --positive-background: var(--green-900);
    --positive-border: var(--green-700);
    --negative-content: var(--red-400);
    --negative-background: var(--red-900);
    --negative-border: var(--red-700);
    --notice-content: var(--orange-400);
    --notice-background: var(--orange-900);
    --notice-border: var(--orange-700);
    --informative-content: var(--blue-400);
    --informative-background: var(--blue-900);
    --informative-border: var(--blue-700);
  }
}