import { Fragment } from 'react';
import { clsx } from 'clsx';
import SidebarSection, {
	SidebarSectionProps,
} from '../molecules/SidebarSection';

import { SidebarHeader } from '../molecules';
import { Divider } from '../atoms';

interface SidebarProps {
	title: string;
	subtitle?: string;
	sections: SidebarSectionProps[];
	isCollapsed?: boolean;
	onToggle?: () => void;
	onItemClick?: (itemId: string) => void;
	className?: string;
}

const Sidebar: React.FC<SidebarProps> = ({
	title,
	subtitle,
	sections,
	isCollapsed = false,
	onToggle,
	className,
}) => {
	return (
		<aside
			className={clsx(
				// Base styles using design tokens
				'flex flex-col h-full',
				'bg-surface-base border-r border-border-base',
				'shadow-subtle',
				'transition-all duration-medium ease-in-out',
				// Width based on collapsed state using design tokens
				isCollapsed ? 'w-20' : 'w-64',
				className,
			)}
			role='navigation'
			aria-label='Main navigation'>
			{onToggle && (
				<SidebarHeader
					title={title}
					subtitle={subtitle}
					isCollapsed={isCollapsed}
					onToggle={onToggle}
				/>
			)}

			<div className='flex-1 overflow-y-auto scrollbar-thin scrollbar-track-transparent scrollbar-thumb-border-base'>
				{sections.map((section, index) => (
					<Fragment key={section.title || index}>
						{index > 0 && <Divider className='mx-200 my-200' />}
						<SidebarSection
							title={section.title || ''}
							isCollapsed={isCollapsed}
							items={section.items}
						/>
					</Fragment>
				))}
			</div>
		</aside>
	);
};

Sidebar.displayName = 'Sidebar';
export default Sidebar;
