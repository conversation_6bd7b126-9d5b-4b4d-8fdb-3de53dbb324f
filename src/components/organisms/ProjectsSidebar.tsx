import { memo, useMemo } from 'react';
import { FolderIcon } from '@heroicons/react/24/outline';
import SideBar from '@/components/organisms/Sidebar';
import type { Project } from './ProjectsGrid';

interface ProjectsSidebarProps {
	projects: Project[];
	isCollapsed: boolean;
	onToggle: () => void;
}

const ProjectsSidebar = memo<ProjectsSidebarProps>(({
	projects,
	isCollapsed,
	onToggle,
}) => {
	const sidebarItems = useMemo(
		() =>
			projects.map((project) => ({
				label: project.name,
				icon: <FolderIcon className='w-4 h-4' />,
				onClick: () => console.log('Project clicked:', project.id),
			})),
		[projects],
	);

	const sidebarSections = useMemo(
		() => [
			{
				title: 'My Projects',
				items: sidebarItems,
			},
		],
		[sidebarItems],
	);

	return (
		<SideBar
			title='Projects'
			sections={sidebarSections}
			isCollapsed={isCollapsed}
			onToggle={onToggle}
		/>
	);
});

ProjectsSidebar.displayName = 'ProjectsSidebar';

export default ProjectsSidebar;
