import { memo } from 'react';
import { <PERSON>, <PERSON><PERSON>eader, CardContent, Typography, DateDisplay } from '@/components/atoms';

/**
 * Example component demonstrating different date formatting options
 * This is for development/documentation purposes
 */
const DateFormattingExample = memo(() => {
	// Sample dates for demonstration
	const now = new Date();
	const twoHoursAgo = new Date(now.getTime() - 2 * 60 * 60 * 1000);
	const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
	const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
	const lastMonth = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
	const lastYear = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);

	const sampleDates = [
		{ label: 'Now', date: now },
		{ label: '2 hours ago', date: twoHoursAgo },
		{ label: 'Yesterday', date: yesterday },
		{ label: 'Last week', date: lastWeek },
		{ label: 'Last month', date: lastMonth },
		{ label: 'Last year', date: lastYear },
	];

	return (
		<div className='space-y-300'>
			<Typography variant='h2'>Date Formatting Examples</Typography>
			
			{sampleDates.map(({ label, date }) => (
				<Card key={label} variant='outlined' size='medium'>
					<CardHeader>
						<Typography variant='h5' weight='semibold'>
							{label}
						</Typography>
						<Typography variant='caption' color='tertiary'>
							Raw: {date.toISOString()}
						</Typography>
					</CardHeader>
					<CardContent>
						<div className='grid grid-cols-1 md:grid-cols-2 gap-200'>
							<div className='space-y-100'>
								<Typography variant='body-sm' weight='medium'>
									Format Types:
								</Typography>
								<div className='space-y-50'>
									<DateDisplay
										date={date}
										format='default'
										prefix='Default: '
										variant='body-sm'
									/>
									<DateDisplay
										date={date}
										format='relative'
										prefix='Relative: '
										variant='body-sm'
									/>
									<DateDisplay
										date={date}
										format='smart'
										prefix='Smart: '
										variant='body-sm'
									/>
									<DateDisplay
										date={date}
										format='project'
										prefix='Project: '
										variant='body-sm'
									/>
									<DateDisplay
										date={date}
										format='full'
										prefix='Full: '
										variant='body-sm'
									/>
								</div>
							</div>
							
							<div className='space-y-100'>
								<Typography variant='body-sm' weight='medium'>
									Custom Formats:
								</Typography>
								<div className='space-y-50'>
									<DateDisplay
										date={date}
										format='custom'
										customFormat='yyyy-MM-dd'
										prefix='ISO Date: '
										variant='body-sm'
									/>
									<DateDisplay
										date={date}
										format='custom'
										customFormat='EEEE, MMMM do'
										prefix='Verbose: '
										variant='body-sm'
									/>
									<DateDisplay
										date={date}
										format='custom'
										customFormat='h:mm a'
										prefix='Time only: '
										variant='body-sm'
									/>
									<DateDisplay
										date={date}
										format='custom'
										customFormat='MMM yyyy'
										prefix='Month/Year: '
										variant='body-sm'
									/>
								</div>
							</div>
						</div>
					</CardContent>
				</Card>
			))}
			
			<Card variant='outlined' size='medium'>
				<CardHeader>
					<Typography variant='h5' weight='semibold'>
						With Tooltips
					</Typography>
				</CardHeader>
				<CardContent>
					<div className='space-y-100'>
						<Typography variant='body-sm' color='secondary'>
							Hover over these dates to see full date tooltips:
						</Typography>
						<div className='space-y-50'>
							<DateDisplay
								date={twoHoursAgo}
								format='relative'
								prefix='Created: '
								showTooltip
								variant='body-sm'
								color='secondary'
							/>
							<DateDisplay
								date={lastWeek}
								format='project'
								prefix='Updated: '
								showTooltip
								variant='body-sm'
								color='secondary'
							/>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
});

DateFormattingExample.displayName = 'DateFormattingExample';

export default DateFormattingExample;
