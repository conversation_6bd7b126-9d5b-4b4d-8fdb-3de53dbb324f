import { useState, useCallback } from 'react';
import { useRouter } from 'next/router';

// Component imports
import AppTemplate from '@/components/templates/AppTemplate';
import ProjectsHeader from '@/components/organisms/ProjectsHeader';
import ProjectsGrid from '@/components/organisms/ProjectsGrid';
import ProjectsSidebar from '@/components/organisms/ProjectsSidebar';
import { NextPageWithLayout } from '@/pages/_app';

// Hooks
import { useProjects } from '@/hooks/useProjects';
import type { Project } from '@/components/organisms/ProjectsGrid';

// Dialog imports
import CreateProjectDialog from '@/components/molecules/CreateProjectDialog';
import EditProjectDialog from '@/components/molecules/EditProjectDialog';
import DeleteProjectDialog from '@/components/molecules/DeleteProjectDialog';
import { Typography } from '@/components/atoms';

const ProjectsPage: NextPageWithLayout = () => {
	const router = useRouter();
	const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
	const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
	const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
	const [selectedProject, setSelectedProject] = useState<Project | null>(null);

	// Use the custom hook for project management
	const {
		projects,
		createProject,
		updateProject,
		deleteProject,
		isLoading,
		canManageProjects,
	} = useProjects();

	const handleOpenDialog = useCallback(() => {
		setIsCreateDialogOpen(true);
	}, []);

	const handleCloseDialog = useCallback(() => {
		setIsCreateDialogOpen(false);
	}, []);

	const handleToggleSidebar = useCallback(() => {
		setIsSidebarCollapsed(!isSidebarCollapsed);
	}, [isSidebarCollapsed]);

	const handleProjectCreated = useCallback(
		async (projectData: { name: string; description: string }) => {
			const newProject = await createProject(projectData);
			if (newProject) {
				setIsCreateDialogOpen(false);
			}
			return newProject;
		},
		[createProject],
	);

	const handleEditProject = useCallback((project: Project) => {
		setSelectedProject(project);
		setIsEditDialogOpen(true);
	}, []);

	const handleDeleteProject = useCallback((project: Project) => {
		setSelectedProject(project);
		setIsDeleteDialogOpen(true);
	}, []);

	const handleProjectUpdated = useCallback(
		async (
			id: string,
			projectData: {
				name: string;
				description: string;
			},
		) => {
			const updatedProject = await updateProject(id, projectData);
			if (updatedProject) {
				setIsEditDialogOpen(false);
				setSelectedProject(null);
			}
			return updatedProject;
		},
		[updateProject],
	);

	const handleProjectDeleted = useCallback(
		async (id: string) => {
			const success = await deleteProject(id);
			if (success) {
				setIsDeleteDialogOpen(false);
				setSelectedProject(null);
			}
			return success;
		},
		[deleteProject],
	);

	const handleCloseEditDialog = useCallback(() => {
		setIsEditDialogOpen(false);
		setSelectedProject(null);
	}, []);

	const handleCloseDeleteDialog = useCallback(() => {
		setIsDeleteDialogOpen(false);
		setSelectedProject(null);
	}, []);

	const handleProjectClick = useCallback(
		(project: Project) => {
			router.push(`/projects/${project.id}`);
		},
		[router],
	);
	return (
		<div className='flex h-svh'>
			{/* Sidebar - hidden on mobile, overlay on tablet, fixed on desktop */}
			<div
				className={`
				${
					isSidebarCollapsed
						? 'hidden lg:block'
						: 'fixed lg:relative inset-0 lg:inset-auto z-50 lg:z-auto'
				}
				${!isSidebarCollapsed ? 'lg:block' : ''}
			`}>
				{!isSidebarCollapsed && (
					<div
						className='absolute inset-0 bg-black/50 lg:hidden'
						onClick={handleToggleSidebar}
					/>
				)}
				<ProjectsSidebar
					projects={projects}
					isCollapsed={isSidebarCollapsed}
					onToggle={handleToggleSidebar}
					onProjectClick={handleProjectClick}
					onCreateProject={() => setIsCreateDialogOpen(true)}
					canManageProjects={canManageProjects}
				/>
			</div>

			{/* Main content */}
			<main className='flex-1 min-w-0 bg-background-base overflow-auto'>
				<div className='p-200 sm:p-300'>
					{/* Mobile header with hamburger */}
					<div className='lg:hidden mb-300'>
						<div className='flex items-center justify-between mb-200'>
							<button
								onClick={handleToggleSidebar}
								className='p-100 rounded-button hover:bg-background-layer-1 focus:outline-none focus:ring-2 focus:ring-accent-border-focus'
								aria-label='Toggle sidebar'>
								<svg
									className='w-6 h-6'
									fill='none'
									stroke='currentColor'
									viewBox='0 0 24 24'>
									<path
										strokeLinecap='round'
										strokeLinejoin='round'
										strokeWidth={2}
										d='M4 6h16M4 12h16M4 18h16'
									/>
								</svg>
							</button>
							<Typography
								variant='h4'
								weight='semibold'>
								Projects
							</Typography>
							<div className='w-10' /> {/* Spacer for centering */}
						</div>
					</div>

					{/* Desktop header */}
					<div className='hidden lg:block'>
						<ProjectsHeader
							onCreateProject={handleOpenDialog}
							canManageProjects={canManageProjects}
						/>
					</div>

					{/* Projects grid */}
					<ProjectsGrid
						projects={projects}
						isLoading={isLoading}
						canManageProjects={canManageProjects}
						onEditProject={handleEditProject}
						onDeleteProject={handleDeleteProject}
					/>
				</div>
			</main>

			{/* Create Project Dialog */}
			{isCreateDialogOpen && (
				<CreateProjectDialog
					isOpen={isCreateDialogOpen}
					onClose={handleCloseDialog}
					onProjectCreated={handleProjectCreated}
				/>
			)}

			{/* Edit Project Dialog */}
			{isEditDialogOpen && (
				<EditProjectDialog
					isOpen={isEditDialogOpen}
					onClose={handleCloseEditDialog}
					project={selectedProject}
					onProjectUpdated={handleProjectUpdated}
				/>
			)}

			{/* Delete Project Dialog */}
			{isDeleteDialogOpen && (
				<DeleteProjectDialog
					isOpen={isDeleteDialogOpen}
					onClose={handleCloseDeleteDialog}
					project={selectedProject}
					onProjectDeleted={handleProjectDeleted}
				/>
			)}
		</div>
	);
};

ProjectsPage.getLayout = (page) => {
	return <AppTemplate>{page}</AppTemplate>;
};

export default ProjectsPage;
