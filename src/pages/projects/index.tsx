import { ThemeToggle } from '@/components/molecules';
import { SideBar } from '@/components/organisms';
import { AppTemplate } from '@/components/templates';
import { NextPageWithLayout } from '@/pages/_app';
import { FolderIcon, PlusIcon } from '@heroicons/react/24/outline';
import { useEffect, useState } from 'react';
import {
	Card,
	CardHeader,
	CardContent,
	CardFooter,
	Button,
	Typography,
	CardTitle,
	CardDescription,
} from '@/components/atoms';
import { ProjectAPI } from '@/api/ProjectApi';
import {
	extractApiData,
	getApiErrorMessage,
	isApiSuccess,
} from '@/common/utils/apiResponse';

// Define the Project interface
interface Project {
	id: string;
	name: string;
	description: string;
	createdAt?: string;
	updatedAt?: string;
}

const ProjectsPage: NextPageWithLayout = () => {
	const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
	const [projects, setProjects] = useState<Project[]>([]);

	useEffect(() => {
		const fetchProjects = async () => {
			try {
				const api = new ProjectAPI();
				const response = await api.getProjects();
				if (isApiSuccess(response)) {
					const projectsData = extractApiData<Project[]>(response);
					setProjects(projectsData || []);
				} else {
					const errorMessage = getApiErrorMessage(response);
					console.error('Failed to fetch projects:', errorMessage);
				}
			} catch (error) {
				console.error('Error fetching projects:', error);
			}
		};
		fetchProjects();
	}, []);
	const items = projects.map((project) => ({
		label: project.name,
		icon: <FolderIcon />,
		onClick: () => console.log('Project clicked'),
	}));
	return (
		<div className='flex h-svh'>
			<SideBar
				title='Projects'
				sections={[
					{
						title: 'My Projects',
						items,
					},
				]}
				isCollapsed={isSidebarCollapsed}
				onToggle={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
			/>
			<main className='flex-1 p-300 bg-background-base overflow-auto'>
				{/* Header Section */}
				<div className='mb-400'>
					<div className='flex justify-between items-center mb-200'>
						<div>
							<Typography
								variant='h1'
								responsive>
								Projects
							</Typography>
							<Typography
								variant='body-lg'
								color='secondary'
								className='mt-2'>
								Manage and track your creative projects with our enhanced design
								system.
							</Typography>
						</div>
						<div className='flex items-center gap-150'>
							<ThemeToggle />
							<Button
								variant='primary'
								size='md'>
								<PlusIcon className='w-4 h-4 mr-2' />
								New Project
							</Button>
						</div>
					</div>
				</div>

				{/* Projects Grid */}
				<div className='grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-300 mb-400'>
					{projects.map((project) => (
						<Card
							key={project.id}
							variant='outlined'
							size='medium'
							interactive
							className='h-full'>
							<CardHeader>
								<CardTitle>{project.name}</CardTitle>
								<CardDescription>{project.description}</CardDescription>
							</CardHeader>
							<CardContent>
								<Typography
									variant='body'
									color='secondary'>
									Created: {project.createdAt}
								</Typography>
								<Typography
									variant='body'
									color='secondary'>
									Updated: {project.updatedAt}
								</Typography>
							</CardContent>
							<CardFooter>
								<Button
									variant='primary'
									size='sm'>
									View
								</Button>
							</CardFooter>
						</Card>
					))}
				</div>
			</main>
		</div>
	);
};

ProjectsPage.getLayout = (page) => {
	return <AppTemplate>{page}</AppTemplate>;
};

export default ProjectsPage;
