@import './design-tokens.css';
@import "tailwindcss";

/**
 * Tailwind CSS v4 Theme Configuration
 *
 * This file contains ONLY the @theme directive that creates utility classes
 * from the design token values defined in design-tokens.css.
 *
 * All token values are defined in design-tokens.css and referenced here
 * to create the corresponding Tailwind utility classes.
 */

/* Tailwind v4 @theme directive - this creates the utility classes */
@theme {
  /* Colors - these create bg-*, text-*, border-* utilities */
  --color-background-base: var(--background-base);
  --color-background-layer-1: var(--background-layer-1);
  --color-background-layer-2: var(--background-layer-2);

  --color-surface-base: var(--surface-base);
  --color-surface-raised: var(--surface-raised);
  --color-surface-overlay: var(--surface-overlay);

  --color-text-primary: var(--text-primary);
  --color-text-secondary: var(--text-secondary);
  --color-text-tertiary: var(--text-tertiary);
  --color-text-disabled: var(--text-disabled);
  --color-text-inverse: var(--text-inverse);

  --color-border-base: var(--border-base);
  --color-border-strong: var(--border-strong);
  --color-border-subtle: var(--border-subtle);

  --color-accent-content-default: var(--accent-content-default);
  --color-accent-content-hover: var(--accent-content-hover);
  --color-accent-content-down: var(--accent-content-down);
  --color-accent-content-focus: var(--accent-content-focus);
  --color-accent-background-default: var(--accent-background-default);
  --color-accent-background-hover: var(--accent-background-hover);
  --color-accent-background-down: var(--accent-background-down);
  --color-accent-border-default: var(--accent-border-default);
  --color-accent-border-hover: var(--accent-border-hover);
  --color-accent-border-focus: var(--accent-border-focus);

  --color-positive-content: var(--positive-content);
  --color-positive-background: var(--positive-background);
  --color-positive-border: var(--positive-border);
  --color-negative-content: var(--negative-content);
  --color-negative-background: var(--negative-background);
  --color-negative-border: var(--negative-border);
  --color-notice-content: var(--notice-content);
  --color-notice-background: var(--notice-background);
  --color-notice-border: var(--notice-border);
  --color-informative-content: var(--informative-content);
  --color-informative-background: var(--informative-background);
  --color-informative-border: var(--informative-border);

  /* Spacing - these create p-*, m-*, gap-*, etc. utilities */
  --spacing-25: var(--spacing-component-xs);
  --spacing-50: var(--spacing-component-sm);
  --spacing-75: var(--spacing-component-sm);
  --spacing-100: var(--spacing-component-base);
  --spacing-125: var(--spacing-component-base);
  --spacing-150: var(--spacing-component-md);
  --spacing-200: var(--spacing-component-lg);
  --spacing-250: var(--spacing-component-lg);
  --spacing-300: var(--spacing-component-xl);
  --spacing-400: var(--spacing-component-2xl);
  --spacing-500: var(--spacing-component-2xl);
  --spacing-600: var(--spacing-component-2xl);
  --spacing-700: var(--spacing-component-2xl);
  --spacing-800: var(--spacing-component-2xl);
  --spacing-900: var(--spacing-component-2xl);
  --spacing-1000: var(--spacing-component-2xl);

  /* Typography - these create text-* utilities */
  --font-size-50: var(--text-component-xs);
  --font-size-75: var(--text-component-sm);
  --font-size-100: var(--text-component-base);
  --font-size-200: var(--text-component-lg);
  --font-size-300: var(--text-component-xl);
  --font-size-400: var(--text-component-2xl);
  --font-size-500: var(--text-component-3xl);
  --font-size-600: var(--text-component-4xl);
  --font-size-700: var(--text-component-4xl);
  --font-size-800: var(--text-component-4xl);
  --font-size-900: var(--text-component-4xl);

  /* Responsive typography - these create text-responsive-* utilities */
  --font-size-responsive-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
  --font-size-responsive-sm: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
  --font-size-responsive-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
  --font-size-responsive-lg: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);
  --font-size-responsive-xl: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem);
  --font-size-responsive-2xl: clamp(1.5rem, 1.3rem + 1vw, 1.875rem);
  --font-size-responsive-3xl: clamp(1.875rem, 1.6rem + 1.375vw, 2.25rem);
  --font-size-responsive-4xl: clamp(2.25rem, 1.9rem + 1.75vw, 3rem);
  --font-size-responsive-5xl: clamp(3rem, 2.5rem + 2.5vw, 4rem);

  /* Border radius - these create rounded-* utilities */
  --radius-small: var(--radius-scale-small);
  --radius-medium: var(--radius-scale-medium);
  --radius-large: var(--radius-scale-large);
  --radius-extra-large: var(--radius-scale-xl);
  --radius-card: var(--radius-card);
  --radius-button: var(--radius-scale-small);
  --radius-input: var(--radius-input);
  --radius-badge: var(--radius-badge);
  --radius-progress: var(--radius-progress);

  /* Shadows - these create shadow-* utilities */
  --shadow-small: var(--shadow-component-small);
  --shadow-medium: var(--shadow-component-medium);
  --shadow-large: var(--shadow-component-large);

  /* Component heights - these create h-* utilities */
  --height-button-small: var(--height-button-small);
  --height-button-medium: var(--height-button-medium);
  --height-button-large: var(--height-button-large);
  --height-input-small: var(--height-input-small);
  --height-input-medium: var(--height-input-medium);
  --height-input-large: var(--height-input-large);

  /* Animation durations - these create duration-* utilities */
  --duration-fast: var(--duration-fast);
  --duration-medium: var(--duration-medium);
  --duration-slow: var(--duration-slow);
}

/* Base styling for the document */
body {
  background-color: var(--background-base);
  color: var(--text-primary);
  font-family: "Geist", ui-sans-serif, system-ui, sans-serif;
  line-height: 1.6;
}

/* Utility classes for responsive design */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Mobile-first responsive utilities */
@media (max-width: 640px) {
  .mobile-stack>*+* {
    margin-top: 0.75rem;
  }

  .mobile-full-width {
    width: 100%;
  }
}