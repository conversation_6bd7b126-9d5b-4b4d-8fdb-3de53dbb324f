import { useState, useCallback, useEffect, useRef } from 'react';
import { createProjectClientSide } from '@/services/projectService';
import { ProjectAPI } from '@/api/ProjectApi';
import { useAuthStore } from '@/providers/auth-store-provider';
import {
	extractApiData,
	getApiErrorMessage,
	isApiSuccess,
} from '@/common/utils/apiResponse';
import type { Project } from '@/components/organisms/ProjectsGrid';

interface UseProjectsProps {
	initialProjects: Project[];
}

interface UseProjectsReturn {
	projects: Project[];
	isCreating: boolean;
	isLoading: boolean;
	error: string | null;
	createProject: (projectData: {
		name: string;
		description: string;
	}) => Promise<Project | null>;
	clearError: () => void;
	refreshProjects: () => void;
}

export function useProjects({
	initialProjects,
}: UseProjectsProps): UseProjectsReturn {
	const [projects, setProjects] = useState<Project[]>(initialProjects);
	const [isCreating, setIsCreating] = useState(false);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	// Get auth state
	const user = useAuthStore((state) => state.user);
	const isHydrated = useAuthStore((state) => state.isHydrated);

	// Track if we've already fetched projects to prevent infinite loops
	const [hasFetched, setHasFetched] = useState(false);
	const fetchingRef = useRef(false);
	const abortControllerRef = useRef<AbortController | null>(null);

	// Fetch projects on client-side after auth is hydrated
	useEffect(() => {
		// Only fetch if:
		// 1. Auth store is hydrated (client-side)
		// 2. User is authenticated
		// 3. No initial projects (SSR returned empty)
		// 4. Haven't already fetched
		// 5. Not currently fetching
		if (
			isHydrated &&
			user &&
			initialProjects.length === 0 &&
			!hasFetched &&
			!fetchingRef.current
		) {
			const fetchProjects = async () => {
				// Cancel any previous request
				if (abortControllerRef.current) {
					abortControllerRef.current.abort();
				}

				// Create new abort controller for this request
				abortControllerRef.current = new AbortController();

				fetchingRef.current = true;
				setIsLoading(true);
				setError(null);
				setHasFetched(true);

				try {
					const api = new ProjectAPI();
					const response = await api.getProjects();
					if (isApiSuccess(response)) {
						const projectsData = extractApiData<Project[]>(response);
						setProjects(projectsData || []);
					} else {
						const errorMessage = getApiErrorMessage(response);
						console.error('Failed to fetch projects:', errorMessage);
						setError(errorMessage);
					}
				} catch (error) {
					// Don't set error if request was aborted
					if (error instanceof Error && error.name !== 'AbortError') {
						console.error('Error fetching projects:', error);
						setError(error.message);
					} else if (!(error instanceof Error)) {
						console.error('Error fetching projects:', error);
						setError('Failed to fetch projects');
					}
				} finally {
					setIsLoading(false);
					fetchingRef.current = false;
					abortControllerRef.current = null;
				}
			};

			fetchProjects();
		}

		// Clear projects if user logs out
		if (isHydrated && !user && hasFetched) {
			setProjects([]);
			setError(null);
			setHasFetched(false);
		}

		// Cleanup function to abort any pending requests
		return () => {
			if (abortControllerRef.current) {
				abortControllerRef.current.abort();
				abortControllerRef.current = null;
			}
		};
	}, [isHydrated, user, initialProjects.length, hasFetched]);

	const createProject = useCallback(
		async (projectData: { name: string; description: string }) => {
			setIsCreating(true);
			setError(null);

			const result = await createProjectClientSide(projectData);

			if (result.project) {
				setProjects((prev) => [result.project!, ...prev]);
				setIsCreating(false);
				return result.project;
			} else {
				setError(result.error || 'Failed to create project');
				setIsCreating(false);
				return null;
			}
		},
		[],
	);

	const clearError = useCallback(() => {
		setError(null);
	}, []);

	const refreshProjects = useCallback(() => {
		if (user && isHydrated) {
			setHasFetched(false);
			setError(null);
		}
	}, [user, isHydrated]);

	return {
		projects,
		isCreating,
		isLoading,
		error,
		createProject,
		clearError,
		refreshProjects,
	};
}
