import { memo } from 'react';
import { PencilIcon, TrashIcon } from '@heroicons/react/24/outline';
import {
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON>po<PERSON>,
	Card<PERSON><PERSON>le,
	CardDescription,
	Badge,
} from '@/components/atoms';
import ProjectCardSkeleton from '@/components/molecules/ProjectCardSkeleton';

export type ProjectStatus =
	| 'DRAFT'
	| 'ACTIVE'
	| 'COMPLETED'
	| 'ARCHIVED'
	| 'ON_HOLD';

export interface Project {
	id: string;
	name: string;
	description: string;
	status: ProjectStatus;
	createdAt?: string;
	updatedAt?: string;
}

interface ProjectsGridProps {
	projects: Project[];
	isLoading?: boolean;
	canManageProjects?: boolean;
	onEditProject?: (project: Project) => void;
	onDeleteProject?: (project: Project) => void;
}

// Helper function to get badge variant and text for project status
const getStatusBadge = (status: ProjectStatus) => {
	switch (status) {
		case 'DRAFT':
			return {
				variant: 'neutral' as const,
				text: 'Draft',
			};
		case 'ACTIVE':
			return {
				variant: 'positive' as const,
				text: 'Active',
			};
		case 'COMPLETED':
			return {
				variant: 'accent' as const,
				text: 'Completed',
			};
		case 'ARCHIVED':
			return {
				variant: 'informative' as const,
				text: 'Archived',
			};
		case 'ON_HOLD':
			return {
				variant: 'notice' as const,
				text: 'On Hold',
			};
		default:
			return {
				variant: 'neutral' as const,
				text: 'Unknown',
			};
	}
};

const ProjectCard = memo<{
	project: Project;
	canManageProjects?: boolean;
	onEditProject?: (project: Project) => void;
	onDeleteProject?: (project: Project) => void;
}>(({ project, canManageProjects, onEditProject, onDeleteProject }) => {
	const statusBadge = getStatusBadge(project.status);

	return (
		<Card
			variant='outlined'
			size='medium'
			interactive
			className='h-full'>
			<CardHeader>
				<div className='flex items-start justify-between mb-100'>
					<CardTitle className='flex-1'>{project.name}</CardTitle>
					<Badge
						variant={statusBadge.variant}
						size='small'
						emphasis='subtle'>
						{statusBadge.text}
					</Badge>
				</div>
				<CardDescription>{project.description}</CardDescription>
			</CardHeader>
			<CardContent>
				<Typography
					variant='body'
					color='secondary'>
					Created: {project.createdAt}
				</Typography>
				<Typography
					variant='body'
					color='secondary'>
					Updated: {project.updatedAt}
				</Typography>
			</CardContent>
			<CardFooter>
				<div className='flex gap-150'>
					<Button
						variant='primary'
						size='sm'>
						View
					</Button>
					{canManageProjects && (
						<>
							<Button
								variant='secondary'
								size='sm'
								onClick={() => onEditProject?.(project)}
								aria-label={`Edit ${project.name}`}>
								<PencilIcon className='w-4 h-4' />
							</Button>
							<Button
								variant='destructive'
								size='sm'
								onClick={() => onDeleteProject?.(project)}
								aria-label={`Delete ${project.name}`}>
								<TrashIcon className='w-4 h-4' />
							</Button>
						</>
					)}
				</div>
			</CardFooter>
		</Card>
	);
});

ProjectCard.displayName = 'ProjectCard';

const ProjectsGrid = memo<ProjectsGridProps>(
	({
		projects,
		isLoading = false,
		canManageProjects = false,
		onEditProject,
		onDeleteProject,
	}) => {
		if (isLoading) {
			// Show skeleton cards while loading for better UX
			return (
				<div className='grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-300 mb-400'>
					{Array.from({ length: 6 }).map((_, index) => (
						<ProjectCardSkeleton key={index} />
					))}
				</div>
			);
		}

		if (projects.length === 0) {
			return (
				<div className='flex flex-col items-center justify-center py-12'>
					<Typography
						variant='h3'
						color='secondary'
						className='mb-2'>
						No projects yet
					</Typography>
					<Typography
						variant='body'
						color='tertiary'>
						Create your first project to get started
					</Typography>
				</div>
			);
		}

		return (
			<div className='grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-300 mb-400'>
				{projects.map((project) => (
					<ProjectCard
						key={project.id}
						project={project}
						canManageProjects={canManageProjects}
						onEditProject={onEditProject}
						onDeleteProject={onDeleteProject}
					/>
				))}
			</div>
		);
	},
);

ProjectsGrid.displayName = 'ProjectsGrid';

export default ProjectsGrid;
