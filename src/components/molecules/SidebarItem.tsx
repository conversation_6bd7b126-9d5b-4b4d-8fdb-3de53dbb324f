import { clsx } from 'clsx';
import { Typography } from '../atoms';

export interface SidebarItemProps {
	icon: React.ReactNode;
	label: string;
	isActive?: boolean;
	isCollapsed?: boolean;
	onClick?: () => void;
	href?: string;
	badge?: React.ReactNode;
	disabled?: boolean;
}

const SidebarItem: React.FC<SidebarItemProps> = ({
	icon,
	label,
	isActive = false,
	isCollapsed = false,
	onClick,
	href,
	badge,
	disabled = false,
}) => {
	const baseClasses = clsx(
		// Layout using design tokens
		'w-full flex items-center',
		'transition-all duration-medium ease-in-out',
		'rounded-button',
		// Spacing using design tokens
		isCollapsed ? 'justify-center p-100' : 'justify-start px-100 py-75',
		// Active states using design tokens
		isActive && [
			'bg-accent-background-default text-text-inverse',
			'shadow-small',
			'border border-accent-border-default',
		],
		// Inactive states using design tokens
		!isActive && [
			'text-text-secondary hover:text-text-primary',
			'hover:bg-background-layer-1',
			'border border-transparent',
		],
		// Disabled state using design tokens
		disabled && 'opacity-50 cursor-not-allowed',
		// Focus styles using design tokens
		'focus:outline-none focus:ring-2 focus:ring-accent-border-focus focus:ring-offset-2 focus:ring-offset-surface-base',
	);

	const content = (
		<>
			<span
				className={clsx(
					'flex-shrink-0 w-4 h-4',
					!isCollapsed && 'mr-150',
					isActive ? 'text-text-inverse' : 'text-current',
				)}>
				{icon}
			</span>
			{!isCollapsed && (
				<>
					<Typography
						variant='body-sm'
						weight='medium'
						color={isActive ? 'inverse' : 'primary'}
						className='truncate flex-1'>
						{label}
					</Typography>
					{badge && <span className='ml-100 flex-shrink-0'>{badge}</span>}
				</>
			)}
		</>
	);

	if (href) {
		return (
			<a
				href={href}
				className={baseClasses}
				onClick={onClick}
				aria-label={isCollapsed ? label : undefined}
				title={isCollapsed ? label : undefined}>
				{content}
			</a>
		);
	}

	return (
		<button
			type='button'
			className={baseClasses}
			onClick={onClick}
			disabled={disabled}
			aria-label={isCollapsed ? label : undefined}
			title={isCollapsed ? label : undefined}>
			{content}
		</button>
	);
};
SidebarItem.displayName = 'SidebarItem';
export default SidebarItem;
