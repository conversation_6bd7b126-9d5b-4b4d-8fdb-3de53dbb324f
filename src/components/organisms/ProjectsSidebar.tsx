import { memo, useMemo } from 'react';
import {
	FolderIcon,
	PlusIcon,
	ArchiveBoxIcon,
	ClockIcon,
	CheckCircleIcon,
	PlayIcon,
} from '@heroicons/react/24/outline';
import SideBar from '@/components/organisms/Sidebar';
import { Badge } from '@/components/atoms';
import type { Project, ProjectStatus } from './ProjectsGrid';

interface ProjectsSidebarProps {
	projects: Project[];
	isCollapsed: boolean;
	onToggle: () => void;
	onProjectClick?: (project: Project) => void;
	onCreateProject?: () => void;
	activeProjectId?: string;
	canManageProjects?: boolean;
}

// Helper function to get icon for project status
const getStatusIcon = (status: ProjectStatus) => {
	switch (status) {
		case 'DRAFT':
			return <FolderIcon className='w-4 h-4' />;
		case 'ACTIVE':
			return <PlayIcon className='w-4 h-4' />;
		case 'COMPLETED':
			return <CheckCircleIcon className='w-4 h-4' />;
		case 'ARCHIVED':
			return <ArchiveBoxIcon className='w-4 h-4' />;
		case 'ON_HOLD':
			return <ClockIcon className='w-4 h-4' />;
		default:
			return <FolderIcon className='w-4 h-4' />;
	}
};

// Helper function to get badge for project status
const getStatusBadge = (status: ProjectStatus, isCollapsed: boolean) => {
	if (isCollapsed) return null;

	const badgeConfig = {
		DRAFT: { variant: 'neutral' as const, text: 'Draft' },
		ACTIVE: { variant: 'positive' as const, text: 'Active' },
		COMPLETED: { variant: 'accent' as const, text: 'Done' },
		ARCHIVED: { variant: 'informative' as const, text: 'Archived' },
		ON_HOLD: { variant: 'notice' as const, text: 'Hold' },
	};

	const config = badgeConfig[status];
	return (
		<Badge
			variant={config.variant}
			size='small'
			emphasis='subtle'>
			{config.text}
		</Badge>
	);
};

const ProjectsSidebar = memo<ProjectsSidebarProps>(
	({
		projects,
		isCollapsed,
		onToggle,
		onProjectClick,
		onCreateProject,
		activeProjectId,
		canManageProjects = false,
	}) => {
		// Create sidebar items for projects
		const projectItems = useMemo(
			() =>
				projects.map((project) => ({
					label: project.name,
					icon: getStatusIcon(project.status),
					badge: getStatusBadge(project.status, isCollapsed),
					isActive: activeProjectId === project.id,
					onClick: () => onProjectClick?.(project),
				})),
			[projects, isCollapsed, activeProjectId, onProjectClick],
		);

		// Create "New Project" item for admins
		const newProjectItem = useMemo(() => {
			if (!canManageProjects) return null;

			return {
				label: 'New Project',
				icon: <PlusIcon className='w-4 h-4' />,
				onClick: onCreateProject,
			};
		}, [canManageProjects, onCreateProject]);

		// Create sidebar sections
		const sidebarSections = useMemo(() => {
			const sections = [];

			// Add "New Project" section for admins
			if (newProjectItem) {
				sections.push({
					title: 'Actions',
					items: [newProjectItem],
				});
			}

			// Add projects section
			if (projectItems.length > 0) {
				sections.push({
					title: `My Projects (${projects.length})`,
					items: projectItems,
				});
			} else if (!canManageProjects) {
				// Show empty state for users who can't create projects
				sections.push({
					title: 'Projects',
					items: [{
						label: 'No projects available',
						icon: <FolderIcon className='w-4 h-4' />,
						disabled: true,
						onClick: () => {},
					}],
				});
			}

			return sections;
		}, [newProjectItem, projectItems, projects.length, canManageProjects]);

		return (
			<SideBar
				title='Projects'
				subtitle={`${projects.length} project${
					projects.length !== 1 ? 's' : ''
				}`}
				sections={sidebarSections}
				isCollapsed={isCollapsed}
				onToggle={onToggle}
			/>
		);
	},
);

ProjectsSidebar.displayName = 'ProjectsSidebar';

export default ProjectsSidebar;
