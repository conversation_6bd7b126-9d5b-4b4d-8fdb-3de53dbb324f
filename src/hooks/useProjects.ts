import { useState, useCallback, useEffect } from 'react';
import {
	createProjectClientSide,
	updateProjectClientSide,
	deleteProjectClientSide,
} from '@/services/projectService';
import { ProjectAPI } from '@/api/ProjectApi';
import { useAuthStore } from '@/providers/auth-store-provider';
import {
	extractApiData,
	getApiErrorMessage,
	isApiSuccess,
} from '@/common/utils/apiResponse';
import type { Project } from '@/components/organisms/ProjectsGrid';

interface UseProjectsProps {
	initialProjects: Project[];
}

interface UseProjectsReturn {
	projects: Project[];
	isCreating: boolean;
	isUpdating: boolean;
	isDeleting: boolean;
	isLoading: boolean;
	error: string | null;
	canManageProjects: boolean; // Role-based permission
	createProject: (projectData: {
		name: string;
		description: string;
	}) => Promise<Project | null>;
	updateProject: (
		id: string,
		projectData: {
			name: string;
			description: string;
		},
	) => Promise<Project | null>;
	deleteProject: (id: string) => Promise<boolean>;
	clearError: () => void;
}

export function useProjects({
	initialProjects,
}: UseProjectsProps): UseProjectsReturn {
	const [projects, setProjects] = useState<Project[]>(initialProjects);
	const [isCreating, setIsCreating] = useState(false);
	const [isUpdating, setIsUpdating] = useState(false);
	const [isDeleting, setIsDeleting] = useState(false);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	// Get auth state
	const user = useAuthStore((state) => state.user);
	const isHydrated = useAuthStore((state) => state.isHydrated);

	// Role-based permission check
	const canManageProjects = user?.role === 'ADMIN';

	// Simple effect: only fetch if we have no projects and user is authenticated
	useEffect(() => {
		if (isHydrated && user && projects.length === 0) {
			const fetchProjects = async () => {
				setIsLoading(true);
				setError(null);

				try {
					const api = new ProjectAPI();
					const response = await api.getProjects();
					if (isApiSuccess(response)) {
						const projectsData = extractApiData<Project[]>(response);
						setProjects(projectsData || []);
					} else {
						const errorMessage = getApiErrorMessage(response);
						setError(errorMessage);
					}
				} catch (error) {
					setError(
						error instanceof Error ? error.message : 'Failed to fetch projects',
					);
				} finally {
					setIsLoading(false);
				}
			};

			fetchProjects();
		}
	}, [isHydrated, user]); // Clean dependencies - only what we actually need

	const createProject = useCallback(
		async (projectData: { name: string; description: string }) => {
			if (!canManageProjects) {
				setError('You do not have permission to create projects');
				return null;
			}

			setIsCreating(true);
			setError(null);

			const result = await createProjectClientSide(projectData);

			if (result.project) {
				setProjects((prev) => [result.project!, ...prev]);
				setIsCreating(false);
				return result.project;
			} else {
				setError(result.error || 'Failed to create project');
				setIsCreating(false);
				return null;
			}
		},
		[canManageProjects],
	);

	const updateProject = useCallback(
		async (id: string, projectData: { name: string; description: string }) => {
			if (!canManageProjects) {
				setError('You do not have permission to update projects');
				return null;
			}

			setIsUpdating(true);
			setError(null);

			const result = await updateProjectClientSide(id, projectData);

			if (result.project) {
				setProjects((prev) =>
					prev.map((project) =>
						project.id === id ? result.project! : project,
					),
				);
				setIsUpdating(false);
				return result.project;
			} else {
				setError(result.error || 'Failed to update project');
				setIsUpdating(false);
				return null;
			}
		},
		[canManageProjects],
	);

	const deleteProject = useCallback(
		async (id: string) => {
			if (!canManageProjects) {
				setError('You do not have permission to delete projects');
				return false;
			}

			setIsDeleting(true);
			setError(null);

			const result = await deleteProjectClientSide(id);

			if (result.success) {
				setProjects((prev) => prev.filter((project) => project.id !== id));
				setIsDeleting(false);
				return true;
			} else {
				setError(result.error || 'Failed to delete project');
				setIsDeleting(false);
				return false;
			}
		},
		[canManageProjects],
	);

	const clearError = useCallback(() => {
		setError(null);
	}, []);

	return {
		projects,
		isCreating,
		isUpdating,
		isDeleting,
		isLoading,
		error,
		canManageProjects,
		createProject,
		updateProject,
		deleteProject,
		clearError,
	};
}
