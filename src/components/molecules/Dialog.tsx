import {
	Dialog as HeadlessDialog,
	DialogPanel,
	DialogTitle,
	DialogBackdrop,
} from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { clsx } from 'clsx';
import { Button, Typography } from '@/components/atoms';

interface DialogProps {
	isOpen: boolean;
	onClose: () => void;
	title?: string;
	description?: string;
	children: React.ReactNode;
	size?: 'sm' | 'md' | 'lg' | 'xl';
	showCloseButton?: boolean;
	className?: string;
}

const dialogSizes = {
	sm: 'max-w-sm mx-200 sm:mx-auto w-full sm:w-auto',
	md: 'max-w-md mx-200 sm:mx-auto w-full sm:w-auto',
	lg: 'max-w-lg mx-200 sm:mx-auto w-full sm:w-auto',
	xl: 'max-w-xl mx-200 sm:mx-auto w-full sm:w-auto',
};

const Dialog: React.FC<DialogProps> = ({
	isOpen,
	onClose,
	title,
	description,
	children,
	size = 'md',
	showCloseButton = true,
	className,
}) => {
	return (
		<HeadlessDialog
			open={isOpen}
			onClose={onClose}
			className='relative z-50'>
			{/* Backdrop */}
			<DialogBackdrop
				className={clsx(
					'fixed inset-0 bg-gray-900/50 backdrop-blur-sm',
					'transition-opacity duration-medium ease-in-out',
					'data-[closed]:opacity-0',
				)}
			/>

			{/* Dialog container */}
			<div className='fixed inset-0 flex items-end sm:items-center justify-center p-0 sm:p-200'>
				<DialogPanel
					className={clsx(
						// Base styles
						'w-full rounded-t-card sm:rounded-card bg-surface-base shadow-large',
						'border-t border-l border-r sm:border border-border-base',
						'transition-all duration-medium ease-in-out',
						'max-h-[90vh] sm:max-h-[80vh] overflow-hidden',
						// Animation states
						'data-[closed]:translate-y-full sm:data-[closed]:scale-95 data-[closed]:opacity-0',
						'data-[enter]:duration-medium data-[enter]:ease-out',
						'data-[leave]:duration-fast data-[leave]:ease-in',
						// Size
						dialogSizes[size],
						className,
					)}>
					{/* Header */}
					{(title || showCloseButton) && (
						<div className='flex items-start justify-between p-300 border-b border-border-base'>
							<div className='flex-1'>
								{title && (
									<DialogTitle>
										<Typography
											variant='h3'
											as='span'
											className='mb-100'>
											{title}
										</Typography>
									</DialogTitle>
								)}
								{description && (
									<Typography
										variant='body'
										color='secondary'>
										{description}
									</Typography>
								)}
							</div>
							{showCloseButton && (
								<Button
									variant='ghost'
									size='sm'
									onClick={onClose}
									className='ml-200 -mt-50 -mr-50'
									aria-label='Close dialog'>
									<XMarkIcon className='w-4 h-4' />
								</Button>
							)}
						</div>
					)}

					{/* Content */}
					<div className='p-200 sm:p-300 overflow-y-auto flex-1'>
						{children}
					</div>
				</DialogPanel>
			</div>
		</HeadlessDialog>
	);
};

export default Dialog;
